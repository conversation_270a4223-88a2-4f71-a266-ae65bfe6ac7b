import { akshareAdapter } from '@/lib/akshare/adapter';
import { TradingAgentState } from '@/lib/langgraph';
import { AKShareNewsItem } from '@/types';

export async function newsAnalystNode(
  state: TradingAgentState
): Promise<Partial<TradingAgentState>> {
  const ticker = state.ticker;
  if (!ticker) {
    throw new Error('Ticker not found in state');
  }

  console.log(`[News Analyst] Analyzing ticker: ${ticker}`);

  try {
    const newsData = await akshareAdapter.invoke<AKShareNewsItem[]>('get_stock_news', {
      symbol: ticker,
    });

    if (!Array.isArray(newsData) || newsData.length === 0) {
      throw new Error('Failed to retrieve valid news data.');
    }

    // 提取最新的3条新闻标题作为摘要
    const summary = newsData
      .slice(0, 3)
      .map((item) => `- ${item.标题}`)
      .join('\n');

    const analysis = `对 ${ticker} 的最新新闻摘要：\n${summary}`;

    const newMessages = [...state.messages, { type: 'ai', content: analysis }];
    const analysisResults = {
      ...state.analysisResults,
      news: {
        summary: analysis,
        data: newsData,
      },
    };

    return { messages: newMessages, analysisResults };
  } catch (error) {
    console.error('[News Analyst] Error:', error);
    const errorMessage = `新闻分析失败: ${error instanceof Error ? error.message : String(error)}`;
    const newMessages = [...state.messages, { type: 'ai', content: errorMessage }];
    return { messages: newMessages };
  }
}
