# HTTPS 生产环境配置 - 包含 Let's Encrypt 自动证书
version: '3.8'

services:
  # TradingAgents Frontend
  frontend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
    container_name: tradingagents-frontend
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://${DOMAIN:-your-domain.com}
      - NEXT_PUBLIC_WS_URL=wss://${DOMAIN:-your-domain.com}/ws
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
      - BACK_END_URL=http://backend:8000
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL 数据库
  mysql:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28
    container_name: tradingagents-mysql
    expose:
      - "3306"
    ports:
      - "13306:3306"  # 保留外部访问端口
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-trading123}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-trading_agents}
      - MYSQL_USER=${MYSQL_USER:-trading_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-trading123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 后端服务 (如果有)
  backend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/ez_trading:**********
    container_name: tradingagents-backend
    expose:
      - "8000"
    environment:
      - DATABASE_URL=mysql://trading_user:trading123@mysql:3306/trading_agents
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - mysql
    profiles:
      - backend

  # Nginx 反向代理 + HTTPS
  nginx:
    image: nginx:alpine
    container_name: tradingagents-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-https.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./certbot/www:/var/www/certbot:ro
      - ./certbot/conf:/etc/letsencrypt:ro
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - frontend
    command: "/bin/sh -c 'while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g \"daemon off;\"'"

  # Certbot for Let's Encrypt
  certbot:
    image: certbot/certbot
    container_name: tradingagents-certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    profiles:
      - certbot

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network

volumes:
  mysql_data:
    driver: local
