# ---- Builder Stage ----
# 1. 在此阶段构建 Next.js 应用
FROM docker.m.daocloud.io/library/node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 lock 文件
COPY package*.json ./

# 安装所有依赖（包括 devDependencies 用于构建）
RUN npm install

# 复制所有项目文件
COPY . .

# 运行构建命令
# 这将根据 next.config.js 生成 .next/standalone 和 .next/static
RUN npm run build

# ---- Runner Stage ----
# 2. 在此阶段创建最终的、轻量的生产镜像
FROM docker.m.daocloud.io/library/node:18-alpine

# 设置工作目录
WORKDIR /app

# 创建非 root 用户以提高安全性
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 从 builder 阶段复制必要的构建产物
# 复制 standalone 输出
COPY --from=builder /app/.next/standalone ./
# 复制 public 文件夹
COPY --from=builder /app/public ./public
# 复制 static 静态资源
COPY --from=builder /app/.next/static ./.next/static

# 设置文件权限
RUN chown -R nextjs:nodejs /app

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 启动应用
# standalone 输出中包含了 server.js
CMD ["node", "server.js"]
