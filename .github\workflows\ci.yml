# TradingAgents Frontend CI/CD Pipeline
# 自动化构建、测试、安全扫描和部署流程
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch: # 允许手动触发
    inputs:
      deploy_environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

permissions:
  contents: read
  security-events: write
  packages: write
  pull-requests: write

env:
  NODE_VERSION: '18'
  # 阿里云容器镜像服务
  ALIYUN_REGISTRY: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
  ALIYUN_NAMESPACE: ez_trading
  ALIYUN_IMAGE_NAME: frontend
  DOCKER_BUILDKIT: 1

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest

    outputs:
      cache-hit: ${{ steps.cache.outputs.cache-hit }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整历史用于分析

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 缓存依赖
        id: cache
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: 安装依赖
        run: npm ci

      - name: ESLint 代码检查
        run: npm run lint
        continue-on-error: false

      - name: TypeScript 类型检查
        run: npm run type-check

  # 单元测试（预留）
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code-quality
    if: false # 暂时禁用，等添加测试后启用

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 运行测试
        run: npm test -- --coverage

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        if: always()
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Docker 构建和推送
  build-and-push:
    name: Docker 构建和推送
    needs: [code-quality]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    permissions:
      security-events: write
      contents: read
      packages: write

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host

      - name: 调试 - 检查环境变量
        run: |
          echo "Registry: ${{ env.ALIYUN_REGISTRY }}"
          echo "Username exists: ${{ secrets.ALIYUN_REGISTRY_USERNAME != '' }}"
          echo "Password exists: ${{ secrets.ALIYUN_REGISTRY_PASSWORD != '' }}"

      - name: 登录到阿里云容器镜像服务
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 提取阿里云镜像元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.ALIYUN_IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/develop' }}
          labels: |
            org.opencontainers.image.title=TradingAgents Frontend
            org.opencontainers.image.description=多智能体大语言模型金融交易框架前端
            org.opencontainers.image.vendor=TradingAgents

      - name: 构建并推送 Docker 镜像到阿里云
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: |
            type=gha
          cache-to: |
            type=gha,mode=max
          platforms: linux/amd64
          build-args: |
            NODE_ENV=production
            BUILDTIME=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}

  # 部署到生产环境
  deploy-prod:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置SSH密钥
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 准备生产环境配置
        run: |
          # 创建生产环境配置文件
          cat > .env.production << 'ENVEOF'
          NODE_ENV=production
          NEXT_PUBLIC_API_BASE_URL=${{ secrets.API_URL }}
          NEXT_PUBLIC_WS_URL=${{ secrets.NEXT_PUBLIC_WS_URL }}
          NEXT_PUBLIC_OPENAI_API_KEY=${{ secrets.NEXT_PUBLIC_OPENAI_API_KEY }}
          NEXT_PUBLIC_FINNHUB_API_KEY=${{ secrets.NEXT_PUBLIC_FINNHUB_API_KEY }}
          BACK_END_URL=${{ secrets.API_URL }}
          MYSQL_ROOT_PASSWORD=${{ secrets.MYSQL_ROOT_PASSWORD }}
          MYSQL_DATABASE=trading_analysis
          MYSQL_USER=trading_user
          MYSQL_PASSWORD=${{ secrets.MYSQL_PASSWORD }}
          ENVEOF

          # 验证文件创建成功
          echo "✅ .env.production 文件已创建"
          echo "文件大小: $(wc -c < .env.production) bytes"
          echo "文件内容预览:"
          head -3 .env.production

      - name: 部署到生产服务器
        run: |
          echo "🚀 开始部署到生产服务器..."

          # 验证本地文件存在
          echo "📋 检查本地文件..."
          if [ ! -f .env.production ]; then
            echo "❌ 本地 .env.production 文件不存在"
            exit 1
          fi
          echo "✅ 本地 .env.production 文件存在"
          echo "文件大小: $(wc -c < .env.production) bytes"

          if [ ! -f docker/docker-compose.prod.frontend.yml ]; then
            echo "❌ 本地 docker-compose.prod.frontend.yml 文件不存在"
            exit 1
          fi
          echo "✅ 本地 docker-compose.prod.frontend.yml 文件存在"

          # 设置部署路径（如果未设置则使用默认值）
          DEPLOY_PATH="${{ secrets.DEPLOY_PATH }}"
          if [ -z "$DEPLOY_PATH" ]; then
            DEPLOY_PATH="/root"
            echo "⚠️ DEPLOY_PATH 未设置，使用默认路径: $DEPLOY_PATH"
          fi

          # 上传配置文件到服务器
          echo "📤 上传配置文件到服务器..."
          echo "目标路径: $DEPLOY_PATH"
          echo "上传 .env.production..."
          scp -i ~/.ssh/deploy_key -v .env.production ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:$DEPLOY_PATH/.env.production
          echo "上传 docker-compose.prod.frontend.yml..."
          scp -i ~/.ssh/deploy_key -v docker/docker-compose.prod.frontend.yml ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:$DEPLOY_PATH/docker-compose.prod.frontend.yml
          echo "✅ 文件上传完成"

          # SSH连接到服务器执行部署
          ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << EOF
            cd $DEPLOY_PATH

            echo "1. 检查文件是否存在..."
            echo "当前目录: \$(pwd)"
            echo "目录内容:"
            ls -la

            if [ ! -f .env.production ]; then
              echo "❌ .env.production 文件不存在"
              echo "尝试查找 .env.production 文件:"
              find . -name ".env.production" -type f 2>/dev/null || echo "未找到 .env.production 文件"
              exit 1
            fi
            echo "✅ .env.production 文件存在，大小: \$(wc -c < .env.production) bytes"

            if [ ! -f docker-compose.prod.frontend.yml ]; then
              echo "❌ docker-compose.prod.frontend.yml 文件不存在"
              echo "尝试查找 docker-compose 文件:"
              find . -name "*docker-compose*" -type f 2>/dev/null || echo "未找到 docker-compose 文件"
              exit 1
            fi
            echo "✅ docker-compose.prod.frontend.yml 文件存在，大小: \$(wc -c < docker-compose.prod.frontend.yml) bytes"

            echo "2. 登录阿里云镜像服务..."
            echo "${{ secrets.ALIYUN_REGISTRY_PASSWORD }}" | docker login --username ${{ secrets.ALIYUN_REGISTRY_USERNAME }} --password-stdin ${{ env.ALIYUN_REGISTRY }}

            echo "3. 拉取最新前端镜像..."
            docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production pull frontend

            echo "4. 停止旧前端服务（保护数据库）..."
            # 只停止前端服务，不影响其他服务
            docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production stop frontend || true
            docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production rm -f frontend || true

            echo "5. 清理前端相关资源（保护数据库）..."
            # 只清理前端容器，不影响数据库
            docker container ls -a --filter "name=tradingagents-frontend" --format "{{.ID}}" | xargs -r docker rm -f || true

            echo "6. 清理前端镜像（保护数据库）..."
            # 只清理前端镜像，保留数据库镜像
            docker images --filter "reference=*frontend*" --format "{{.ID}}" | xargs -r docker rmi -f || true

            echo "7. 启动新前端服务..."
            if ! docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production up -d frontend; then
              echo "❌ 容器启动失败，尝试强制重建..."
              docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production up -d --force-recreate frontend
            fi

            echo "8. 验证容器状态..."
            if docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production ps frontend | grep -q "Up"; then
              echo "✅ 前端容器启动成功"
            else
              echo "❌ 前端容器启动失败，查看日志:"
              docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production logs frontend
              exit 1
            fi

            echo "✅ 前端部署完成"
          EOF

      - name: 健康检查
        run: |
          echo "🔍 执行健康检查..."
          sleep 30

          if ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
           "cd ${{ secrets.DEPLOY_PATH }} && \
            docker-compose -f docker-compose.prod.frontend.yml ps frontend | grep -q ' Up '"; then
            echo "✅ 前端容器运行状态检查通过"
          else
              echo "❌ 前端容器启动失败"
              ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
                "cd ${{ secrets.DEPLOY_PATH }} && \
                 docker-compose -f docker-compose.prod.frontend.yml logs --tail=50 frontend"
              exit 1
          fi

  # 性能测试
  performance-test:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
