# TradingAgents Frontend Docker Environment Variables
# 复制此文件为 .env 并填入实际值

# API Keys
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_FINNHUB_API_KEY=your_finnhub_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here

# API URLs
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
BACK_END_URL=http://localhost:5000

# MySQL Database
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_agents
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123

# 数据库连接字符串 (如果需要)
DATABASE_URL=mysql://trading_user:trading123@mysql:3306/trading_agents
