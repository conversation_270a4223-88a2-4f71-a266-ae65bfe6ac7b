#!/bin/bash

# HTTPS 配置脚本 - 自动配置域名和SSL证书
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo "=== TradingAgents HTTPS 配置脚本 ==="
echo

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    print_error "请使用 root 用户运行此脚本"
    exit 1
fi

# 获取域名
read -p "请输入您的域名 (例如: example.com): " DOMAIN
if [ -z "$DOMAIN" ]; then
    print_error "域名不能为空"
    exit 1
fi

# 获取邮箱
read -p "请输入您的邮箱 (用于 Let's Encrypt): " EMAIL
if [ -z "$EMAIL" ]; then
    print_error "邮箱不能为空"
    exit 1
fi

print_info "域名: $DOMAIN"
print_info "邮箱: $EMAIL"
echo

# 确认配置
read -p "确认配置正确吗? (y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    print_info "已取消配置"
    exit 0
fi

print_step "1. 创建必要的目录"
mkdir -p certbot/conf
mkdir -p certbot/www
mkdir -p ssl
mkdir -p mysql/init

print_step "2. 更新 Nginx 配置文件"
# 替换域名
sed "s/your-domain.com/$DOMAIN/g" nginx-https.conf > nginx-https-configured.conf

print_step "3. 创建环境变量文件"
cat > .env << EOF
# 域名配置
DOMAIN=$DOMAIN
EMAIL=$EMAIL

# 前端配置
NEXT_PUBLIC_API_BASE_URL=https://$DOMAIN
NEXT_PUBLIC_WS_URL=wss://$DOMAIN/ws
BACK_END_URL=http://backend:8000

# 数据库配置
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_agents
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123

# API 密钥 (请根据需要修改)
NEXT_PUBLIC_OPENAI_API_KEY=your-openai-api-key
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key
EOF

print_step "4. 创建临时 Nginx 配置 (用于获取证书)"
cat > nginx-temp.conf << EOF
events {
    worker_connections 1024;
}

http {
    server {
        listen 80;
        server_name $DOMAIN www.$DOMAIN;
        
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }
        
        location / {
            return 200 'Hello World!';
            add_header Content-Type text/plain;
        }
    }
}
EOF

print_step "5. 启动临时 Nginx 容器"
docker run -d --name temp-nginx \
    -p 80:80 \
    -v $(pwd)/nginx-temp.conf:/etc/nginx/nginx.conf:ro \
    -v $(pwd)/certbot/www:/var/www/certbot:ro \
    nginx:alpine

print_step "6. 获取 SSL 证书"
docker run --rm \
    -v $(pwd)/certbot/conf:/etc/letsencrypt \
    -v $(pwd)/certbot/www:/var/www/certbot \
    certbot/certbot certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    -d $DOMAIN \
    -d www.$DOMAIN

if [ $? -eq 0 ]; then
    print_info "SSL 证书获取成功!"
else
    print_error "SSL 证书获取失败"
    docker stop temp-nginx && docker rm temp-nginx
    exit 1
fi

print_step "7. 停止临时容器"
docker stop temp-nginx && docker rm temp-nginx

print_step "8. 复制证书到 SSL 目录"
cp certbot/conf/live/$DOMAIN/fullchain.pem ssl/
cp certbot/conf/live/$DOMAIN/privkey.pem ssl/

print_step "9. 更新 Docker Compose 配置"
# 使用配置好的 nginx 配置文件
cp nginx-https-configured.conf nginx-https.conf

print_step "10. 启动 HTTPS 服务"
docker-compose -f docker-compose.https.yml up -d

print_step "11. 设置证书自动续期"
# 创建续期脚本
cat > renew-cert.sh << 'EOF'
#!/bin/bash
docker-compose -f docker-compose.https.yml run --rm certbot renew
docker-compose -f docker-compose.https.yml exec nginx nginx -s reload
EOF

chmod +x renew-cert.sh

# 添加到 crontab (每天检查一次)
(crontab -l 2>/dev/null; echo "0 12 * * * $(pwd)/renew-cert.sh") | crontab -

print_info "=== HTTPS 配置完成! ==="
echo
print_info "您的网站现在可以通过以下地址访问:"
print_info "https://$DOMAIN"
print_info "https://www.$DOMAIN"
echo
print_info "证书将自动续期，无需手动操作"
echo
print_step "检查服务状态:"
docker-compose -f docker-compose.https.yml ps

print_warning "请确保您的域名 DNS 已正确指向此服务器的 IP 地址"
print_warning "如果访问有问题，请检查防火墙设置，确保开放 80 和 443 端口"
