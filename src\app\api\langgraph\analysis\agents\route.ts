import { NextRequest, NextResponse } from 'next/server';
const BASE_URL = 'https://finnhub.io/api/v1/stock/candle';

export async function GET(request: NextRequest) {
  // 初始化 Finnhub 客户端
  // https://www.npmjs.com/package/finnhub

  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');
    console.log(
      `${BASE_URL}?symbol=AAPL&from=2025-04-01&to=2025-05-01&token=d1ii6e9r01qhsrhfo78gd1ii6e9r01qhsrhfo790`
    );

    const a = await fetch(
      `${BASE_URL}?symbol=688111.SZ&from=2025-04-01&to=2025-05-01&token=d1ii6e9r01qhsrhfo78gd1ii6e9r01qhsrhfo790`
    );
    const data = await a.json();
    console.log(
      data,
      `${BASE_URL}?symbol=AAPL&from=2025-04-01&to=2025-05-01&token=d1ii6e9r01qhsrhfo78gd1ii6e9r01qhsrhfo790`
    );

    if (!analysisId) {
      return NextResponse.json({ error: '分析ID不能为空' }, { status: 400 });
    }

    // 返回代理状态
    return NextResponse.json({
      analysisId,
      agents: [
        {
          id: 'fundamental_analyst',
          name: '基本面分析师',
          status: 'idle',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'technical_analyst',
          name: '技术分析师',
          status: 'idle',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'sentiment_analyst',
          name: '情绪分析师',
          status: 'idle',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'risk_manager',
          name: '风险管理师',
          status: 'idle',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
        {
          id: 'trader',
          name: '交易员',
          status: 'idle',
          progress: 0,
          lastUpdate: new Date().toISOString(),
        },
      ],
    });
  } catch (error) {
    console.error('获取代理状态失败:', error);
    return NextResponse.json({ error: '获取代理状态失败' }, { status: 500 });
  }
}
