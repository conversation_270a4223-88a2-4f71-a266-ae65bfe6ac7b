// 服务端专用的 LangGraph 模块
// 这个文件只在 Node.js 环境中运行

import { BaseMessage, HumanMessage } from '@langchain/core/messages';
import { MemorySaver } from '@langchain/langgraph';
import { EventEmitter } from 'events';
import { akshareAdapter } from './akshare/adapter';
import { createTradingWorkflow } from './langgraph-state';

// 定义状态接口
export interface TradingAgentState {
  messages: BaseMessage[];
  ticker?: string;
  analysisConfig?: any;
  analysisResults?: any;
  tradingDecision?: any;
  riskAssessment?: any;
}

// 定义会话状态接口
export interface SessionState {
  threadId: string;
  messages: BaseMessage[];
  currentStep: string;
  isProcessing: boolean;
  analysisResults?: any;
  tradingDecision?: any;
  error?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// LangGraph 服务类
export class LangGraphService extends EventEmitter {
  private workflow: any;
  private memory: MemorySaver;
  private sessions: Map<string, SessionState> = new Map();
  private isInitialized = false;

  constructor() {
    super();
    this.memory = new MemorySaver();
    this.workflow = createTradingWorkflow();
    this.initialize();
  }

  // 初始化服务，启动依赖项
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    console.log('Initializing LangGraphService...');
    try {
      await akshareAdapter.start();
      this.isInitialized = true;
      console.log('LangGraphService initialized successfully.');
    } catch (error) {
      console.error('Failed to initialize LangGraphService:', error);
      // 在实际应用中，这里可能需要更复杂的重试或错误处理逻辑
      process.exit(1); // 如果关键依赖无法启动，则退出
    }
  }

  // 创建新会话
  public createSession(threadId?: string): string {
    const sessionId = threadId || `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const session: SessionState = {
      threadId: sessionId,
      messages: [],
      currentStep: '',
      isProcessing: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.sessions.set(sessionId, session);
    return sessionId;
  }

  // 获取会话状态
  public getSession(threadId: string): SessionState | null {
    return this.sessions.get(threadId) || null;
  }

  // 更新会话状态
  private updateSession(threadId: string, updates: Partial<SessionState>): void {
    const session = this.sessions.get(threadId);
    if (session) {
      Object.assign(session, updates, { updatedAt: new Date() });
      this.sessions.set(threadId, session);

      // 发射状态更新事件
      this.emit('sessionUpdate', { threadId, session });
    }
  }

  // 发送消息
  public async sendMessage(threadId: string, message: string): Promise<any> {
    let session = this.getSession(threadId);
    if (!session) {
      this.createSession(threadId);
      session = this.getSession(threadId)!;
    }

    try {
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '处理消息...',
        error: null,
      });

      // 添加用户消息
      const humanMessage = new HumanMessage(message);

      // 调用 workflow 执行
      const config = { configurable: { thread_id: threadId } };
      const result = await this.workflow.invoke({ messages: [humanMessage] }, config);

      // 更新会话状态
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '完成',
        messages: result.messages,
      });

      return {
        content: result.messages[result.messages.length - 1].content,
        metadata: {
          threadId,
          timestamp: new Date().toISOString(),
          messageType: 'chat_response',
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '处理失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 分析股票
  public async analyzeStock(threadId: string, ticker: string, config: any = {}): Promise<any> {
    let session = this.getSession(threadId);
    if (!session) {
      this.createSession(threadId);
      session = this.getSession(threadId)!;
    }

    try {
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '开始分析...',
        error: null,
      });

      // 创建分析消息
      const analysisMessage = new HumanMessage(`请分析股票 ${ticker}`);

      // 调用 workflow 执行
      const graphConfig = { configurable: { thread_id: threadId } };
      const result = await this.workflow.invoke(
        {
          messages: [analysisMessage],
          ticker,
          analysisConfig: config,
        },
        graphConfig
      );

      // 更新会话状态
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析完成',
        messages: result.messages,
        analysisResults: result.analysisResults,
        tradingDecision: result.tradingDecision,
      });

      return {
        content: result.messages[result.messages.length - 1].content,
        analysisResults: result.analysisResults,
        tradingDecision: result.tradingDecision,
        metadata: {
          threadId,
          ticker,
          timestamp: new Date().toISOString(),
          messageType: 'analysis_response',
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 流式分析
  public async *streamAnalysis(
    threadId: string,
    ticker: string,
    config: any = {}
  ): AsyncGenerator<any> {
    let session = this.getSession(threadId);
    if (!session) {
      this.createSession(threadId);
      session = this.getSession(threadId)!;
    }

    try {
      // 开始分析
      this.updateSession(threadId, {
        isProcessing: true,
        currentStep: '初始化分析...',
        error: null,
      });

      yield { currentStep: '初始化分析...', progress: 0 };

      // 模拟分析步骤
      const steps = [
        { step: '收集数据...', progress: 20 },
        { step: '基本面分析...', progress: 40 },
        { step: '技术分析...', progress: 60 },
        { step: '情绪分析...', progress: 80 },
        { step: '生成决策...', progress: 100 },
      ];

      for (const stepInfo of steps) {
        this.updateSession(threadId, { currentStep: stepInfo.step });
        yield stepInfo;
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // 执行最终分析
      const result = await this.analyzeStock(threadId, ticker, config);
      yield { ...result, progress: 100 };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.updateSession(threadId, {
        isProcessing: false,
        currentStep: '分析失败',
        error: errorMessage,
      });
      throw error;
    }
  }

  // 清除会话
  public clearSession(threadId: string): void {
    this.sessions.delete(threadId);
    this.emit('sessionCleared', { threadId });
  }

  // 获取所有活跃会话
  public getActiveSessions(): SessionState[] {
    return Array.from(this.sessions.values());
  }
}

// 创建全局服务实例
export const langGraphService = new LangGraphService();
