# HTTPS 手动配置指南

## 🔐 方法一：使用自动化脚本 (推荐)

### 快速配置
```bash
# 1. 上传文件到服务器
- docker-compose.https.yml
- nginx-https.conf
- setup-https.sh

# 2. 运行自动化脚本
chmod +x setup-https.sh
./setup-https.sh

# 按提示输入域名和邮箱即可
```

## 🛠️ 方法二：手动配置步骤

### 1. 准备工作

确保您的域名已正确解析到服务器IP：
```bash
# 检查域名解析
nslookup your-domain.com
ping your-domain.com
```

### 2. 获取SSL证书

#### 选项A：使用 Let's Encrypt (免费)

```bash
# 安装 certbot
apt update
apt install certbot

# 停止现有服务
docker-compose down

# 获取证书
certbot certonly --standalone -d your-domain.com -d www.your-domain.com

# 证书位置
# /etc/letsencrypt/live/your-domain.com/fullchain.pem
# /etc/letsencrypt/live/your-domain.com/privkey.pem
```

#### 选项B：使用已有证书

如果您已有SSL证书，请将证书文件放置到 `ssl/` 目录：
```bash
mkdir -p ssl
# 复制您的证书文件
cp your-cert.pem ssl/fullchain.pem
cp your-key.pem ssl/privkey.pem
```

### 3. 配置 Nginx

#### 修改 nginx-https.conf 文件

```bash
# 替换域名
sed -i 's/your-domain.com/实际域名/g' nginx-https.conf
```

或手动编辑文件，将所有 `your-domain.com` 替换为您的实际域名。

### 4. 配置环境变量

创建 `.env` 文件：
```bash
cat > .env << 'EOF'
# 域名配置
DOMAIN=your-domain.com

# 前端配置
NEXT_PUBLIC_API_BASE_URL=https://your-domain.com
NEXT_PUBLIC_WS_URL=wss://your-domain.com/ws
BACK_END_URL=http://backend:8000

# 数据库配置
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_agents
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123

# API 密钥
NEXT_PUBLIC_OPENAI_API_KEY=your-openai-api-key
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key
EOF
```

### 5. 复制证书文件

```bash
# 创建 ssl 目录
mkdir -p ssl

# 从 Let's Encrypt 复制证书
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/
cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/

# 设置权限
chmod 644 ssl/fullchain.pem
chmod 600 ssl/privkey.pem
```

### 6. 启动 HTTPS 服务

```bash
# 启动服务
docker-compose -f docker-compose.https.yml up -d

# 检查状态
docker-compose -f docker-compose.https.yml ps
```

### 7. 设置证书自动续期

```bash
# 创建续期脚本
cat > renew-cert.sh << 'EOF'
#!/bin/bash
certbot renew --quiet
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/
cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/
docker-compose -f docker-compose.https.yml exec nginx nginx -s reload
EOF

chmod +x renew-cert.sh

# 添加到定时任务
(crontab -l 2>/dev/null; echo "0 3 * * * $(pwd)/renew-cert.sh") | crontab -
```

## 🔧 方法三：使用现有证书文件

如果您已有证书文件：

### 1. 准备证书文件
```bash
mkdir -p ssl
# 将您的证书文件复制到 ssl 目录
# fullchain.pem - 完整证书链
# privkey.pem - 私钥文件
```

### 2. 修改配置文件
```bash
# 编辑 nginx-https.conf
# 将 your-domain.com 替换为您的域名
sed -i 's/your-domain.com/您的域名/g' nginx-https.conf
```

### 3. 启动服务
```bash
docker-compose -f docker-compose.https.yml up -d
```

## 🚀 快速测试

### 检查HTTPS是否工作
```bash
# 测试HTTP重定向
curl -I http://your-domain.com

# 测试HTTPS
curl -I https://your-domain.com

# 检查SSL证书
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

### 检查服务状态
```bash
# 查看容器状态
docker-compose -f docker-compose.https.yml ps

# 查看日志
docker-compose -f docker-compose.https.yml logs nginx
```

## 🛡️ 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
ufw allow 80
ufw allow 443

# CentOS/RHEL
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

### SSL 安全评级
访问 https://www.ssllabs.com/ssltest/ 测试您的SSL配置安全性。

## 📋 故障排除

### 常见问题

1. **证书获取失败**
   - 检查域名解析是否正确
   - 确保80端口未被占用
   - 检查防火墙设置

2. **HTTPS访问失败**
   - 检查证书文件路径
   - 查看Nginx错误日志
   - 确认443端口开放

3. **证书过期**
   - 检查自动续期脚本
   - 手动运行续期命令
   - 重启Nginx服务

### 日志查看
```bash
# Nginx 日志
docker-compose -f docker-compose.https.yml logs nginx

# 证书续期日志
docker-compose -f docker-compose.https.yml logs certbot

# 系统日志
journalctl -u docker
```

## 📞 技术支持

如果遇到问题：
1. 检查域名DNS解析
2. 确认防火墙配置
3. 查看详细错误日志
4. 验证证书文件完整性
