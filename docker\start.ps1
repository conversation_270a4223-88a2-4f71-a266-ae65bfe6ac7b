# TradingAgents Frontend Docker 启动脚本
# PowerShell 脚本

Write-Host "🚀 启动 TradingAgents Frontend Docker 容器..." -ForegroundColor Green

# 检查是否存在 .env 文件
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  未找到 .env 文件，正在复制 .env.example..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "📝 请编辑 .env 文件并填入正确的 API 密钥" -ForegroundColor Cyan
    Write-Host "   - NEXT_PUBLIC_OPENAI_API_KEY" -ForegroundColor Cyan
    Write-Host "   - NEXT_PUBLIC_FINNHUB_API_KEY" -ForegroundColor Cyan
    
    $continue = Read-Host "是否继续启动? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "❌ 启动已取消" -ForegroundColor Red
        exit 1
    }
}

# 选择启动模式
Write-Host ""
Write-Host "请选择启动模式:" -ForegroundColor Cyan
Write-Host "1. 仅前端 (推荐用于开发)" -ForegroundColor White
Write-Host "2. 完整服务 (前端 + MySQL + Nginx)" -ForegroundColor White
Write-Host "3. 前端 + MySQL" -ForegroundColor White

$choice = Read-Host "请输入选择 (1-3)"

switch ($choice) {
    "1" {
        Write-Host "🔧 启动仅前端服务..." -ForegroundColor Blue
        docker-compose -f docker-compose-simple.yml up --build -d
    }
    "2" {
        Write-Host "🔧 启动完整服务..." -ForegroundColor Blue
        docker-compose up --build -d
    }
    "3" {
        Write-Host "🔧 启动前端和数据库..." -ForegroundColor Blue
        docker-compose up frontend mysql --build -d
    }
    default {
        Write-Host "❌ 无效选择，默认启动仅前端服务" -ForegroundColor Yellow
        docker-compose -f docker-compose-simple.yml up --build -d
    }
}

Write-Host ""
Write-Host "✅ 启动完成!" -ForegroundColor Green
Write-Host "🌐 前端地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "📊 查看日志: docker-compose logs -f" -ForegroundColor Cyan
Write-Host "🛑 停止服务: docker-compose down" -ForegroundColor Cyan
