# Docker 镜像构建和推送工作流
name: Docker Build and Push

on:
  push:
    branches: [main, develop]
    tags: ['v*']
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      push_to_registry:
        description: '是否推送到注册表'
        required: true
        default: 'true'
        type: boolean

env:
  # 阿里云容器镜像服务
  ALIYUN_REGISTRY: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
  ALIYUN_NAMESPACE: ez_trading
  ALIYUN_IMAGE_NAME: frontend

jobs:
  docker-build:
    name: Docker 构建和推送
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      security-events: write

    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host

      - name: 登录到阿里云容器镜像服务
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.ALIYUN_IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=staging,enable=${{ github.ref == 'refs/heads/develop' }}
          labels: |
            org.opencontainers.image.title=TradingAgents Frontend
            org.opencontainers.image.description=多智能体大语言模型金融交易框架前端
            org.opencontainers.image.vendor=TradingAgents
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.documentation=https://github.com/${{ github.repository }}/blob/main/README.md

      - name: 构建 Docker 镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' && (github.event.inputs.push_to_registry != 'false') }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            NODE_ENV=production
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            VCS_REF=${{ github.sha }}

      - name: Docker 镜像安全扫描
        if: github.event_name != 'pull_request'
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.ALIYUN_IMAGE_NAME }}:${{ steps.meta.outputs.version }}
          format: 'sarif'
          output: 'docker-security-results.sarif'

      - name: 生成镜像清单
        if: github.event_name != 'pull_request'
        run: |
          echo "## Docker 镜像信息" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像标签**: ${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像摘要**: ${{ steps.build.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建时间**: $(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_STEP_SUMMARY
          echo "- **Git SHA**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

  # 多环境镜像测试
  test-images:
    name: 测试 Docker 镜像
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name != 'pull_request'

    strategy:
      matrix:
        platform: [linux/amd64, linux/arm64]

    steps:
      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到阿里云注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 测试镜像启动
        run: |
          # 提取第一个标签进行测试
          IMAGE_TAG=$(echo "${{ needs.docker-build.outputs.image-tag }}" | head -n1)
          echo "测试镜像: $IMAGE_TAG"

          # 运行容器并检查健康状态
          docker run --rm -d --name test-container \
            -p 3000:3000 \
            -e NODE_ENV=production \
            "$IMAGE_TAG"

          # 等待容器启动
          sleep 30

          # 检查容器是否正在运行
          if docker ps | grep test-container; then
            echo "✅ 容器启动成功"
          else
            echo "❌ 容器启动失败"
            docker logs test-container
            exit 1
          fi

          # 清理
          docker stop test-container || true
