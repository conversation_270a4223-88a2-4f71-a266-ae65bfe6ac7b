#!/bin/bash

# Docker 国内镜像源配置脚本
# 适用于 Linux 和 macOS 系统

echo "=== Docker 国内镜像源配置脚本 ==="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: 未检测到 Docker，请先安装 Docker"
    exit 1
fi

echo "检测到 Docker: $(docker --version)"

# 获取当前脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DAEMON_JSON_PATH="$SCRIPT_DIR/daemon.json"

# 检查 daemon.json 文件是否存在
if [ ! -f "$DAEMON_JSON_PATH" ]; then
    echo "错误: 找不到 daemon.json 文件"
    exit 1
fi

# 检测操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo ""
echo "检测到操作系统: $MACHINE"
echo ""

echo "配置选项:"
echo "1. 自动配置 (Linux)"
echo "2. 手动配置说明"
echo "3. 查看当前配置"
echo "4. 测试镜像源"
echo "5. 备份当前配置"

read -p "请选择操作 (1-5): " choice

case $choice in
    1)
        if [ "$MACHINE" = "Linux" ]; then
            echo ""
            echo "开始自动配置 Linux 系统..."
            
            # 备份现有配置
            if [ -f "/etc/docker/daemon.json" ]; then
                echo "备份现有配置..."
                sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
            fi
            
            # 创建目录
            sudo mkdir -p /etc/docker
            
            # 复制配置文件
            sudo cp "$DAEMON_JSON_PATH" /etc/docker/daemon.json
            
            # 重启 Docker 服务
            echo "重启 Docker 服务..."
            sudo systemctl daemon-reload
            sudo systemctl restart docker
            
            echo "✓ 配置完成"
        else
            echo ""
            echo "macOS 用户请选择选项 2 进行手动配置"
        fi
        ;;
        
    2)
        echo ""
        echo "手动配置说明:"
        echo ""
        if [ "$MACHINE" = "Mac" ]; then
            echo "macOS 配置步骤:"
            echo "1. 打开 Docker Desktop"
            echo "2. 点击菜单栏的 Docker 图标"
            echo "3. 选择 'Preferences' -> 'Docker Engine'"
            echo "4. 将以下配置复制到配置框中:"
        else
            echo "Linux 配置步骤:"
            echo "1. 编辑 /etc/docker/daemon.json 文件"
            echo "2. 将以下配置复制到文件中:"
        fi
        
        echo ""
        echo "--- 配置内容开始 ---"
        cat "$DAEMON_JSON_PATH"
        echo ""
        echo "--- 配置内容结束 ---"
        echo ""
        
        if [ "$MACHINE" = "Mac" ]; then
            echo "5. 点击 'Apply & Restart' 重启 Docker"
        else
            echo "3. 重启 Docker 服务:"
            echo "   sudo systemctl daemon-reload"
            echo "   sudo systemctl restart docker"
        fi
        ;;
        
    3)
        echo ""
        echo "当前 Docker 配置:"
        docker info 2>/dev/null | grep -A 10 "Registry Mirrors" || echo "无法获取镜像源配置"
        ;;
        
    4)
        echo ""
        echo "测试镜像源..."
        echo "正在测试拉取 hello-world 镜像..."
        
        start_time=$(date +%s)
        if docker pull hello-world; then
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            echo "✓ 镜像拉取成功，耗时: ${duration} 秒"
        else
            echo "✗ 镜像拉取失败，请检查网络连接和镜像源配置"
        fi
        ;;
        
    5)
        echo ""
        echo "备份当前配置..."
        
        if [ "$MACHINE" = "Linux" ]; then
            if [ -f "/etc/docker/daemon.json" ]; then
                backup_file="/etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)"
                sudo cp /etc/docker/daemon.json "$backup_file"
                echo "✓ 配置已备份到: $backup_file"
            else
                echo "未找到现有配置文件"
            fi
        else
            echo "macOS 用户请通过 Docker Desktop 界面查看配置"
        fi
        ;;
        
    *)
        echo "无效选择，请重新运行脚本"
        exit 1
        ;;
esac

echo ""
echo "脚本执行完成"
