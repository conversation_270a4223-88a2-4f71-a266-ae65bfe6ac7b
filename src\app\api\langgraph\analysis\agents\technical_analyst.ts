import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { AIMessage } from '@langchain/core/messages';

const technicalAnalystPrompt = PromptTemplate.fromTemplate(`
你是一位专业的技术分析师，专注于分析股票价格走势、交易量和技术指标。
你的任务是识别价格模式、趋势和可能的反转点，为短期和中期交易决策提供依据。

请分析以下股票的技术数据，并提供详细的技术分析报告：

股票: {ticker}
日期: {date}

技术数据:
{technicalData}

请在报告中包含以下内容：
1. 价格趋势分析（短期、中期和长期趋势）
2. 关键支撑位和阻力位
3. 交易量分析
4. 技术指标分析（如MACD、RSI、移动平均线等）
5. 图表形态识别（如头肩顶、双底等）
6. 基于技术面的交易建议

你的分析应该客观、精确，基于技术分析理论和历史数据模式，避免过度解读。
`);

export async function technicalAnalystNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, data, messages } = state;

  if (!data.technicalData) {
    throw new Error('Technical data not found in state');
  }

  console.log(`[Technical Analyst] Analyzing ticker: ${ticker}`);

  try {
    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM,
      temperature: 0.2,
      openAIApiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    const prompt = await technicalAnalystPrompt.format({
      ticker,
      date,
      technicalData: JSON.stringify(data.technicalData.slice(-30), null, 2), // 只取最近30天的数据
    });

    const response = await llm.invoke(prompt);
    const analysisReport = response.content as string;

    const newMessages = [
      ...messages,
      new AIMessage({ content: analysisReport, name: 'TechnicalAnalyst' }),
    ];

    const analysis = {
      ...state.analysis,
      technical: {
        summary: analysisReport.substring(0, 200) + '...',
        report: analysisReport,
      },
    };

    return { messages: newMessages, analysis };
  } catch (error) {
    console.error('[Technical Analyst] Error:', error);
    const errorMessage = `技术分析失败: ${error instanceof Error ? error.message : String(error)}`;
    const newMessages = [
      ...messages,
      new AIMessage({ content: errorMessage, name: 'TechnicalAnalyst' }),
    ];
    return { messages: newMessages };
  }
}
