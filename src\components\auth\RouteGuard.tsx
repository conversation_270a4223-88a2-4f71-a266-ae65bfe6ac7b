'use client';

import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface RouteGuardProps {
  children: React.ReactNode;
}

// 需要登录才能访问的路由
const protectedRoutes = [
  '/tasks',
  '/create-task',
  '/messages',
  '/profile',
];

export function RouteGuard({ children }: RouteGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      // 检查当前路由是否需要登录
      const isProtectedRoute = protectedRoutes.some(route => 
        pathname === route || pathname.startsWith(route + '/')
      );

      if (!isProtectedRoute) {
        // 公开路由，直接放行
        setAuthorized(true);
        setLoading(false);
        return;
      }

      // 需要登录的路由，检查认证状态
      try {
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          setAuthorized(true);
        } else {
          // 未登录，重定向到登录页
          router.push('/login');
          return;
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        router.push('/login');
        return;
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [pathname, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!authorized) {
    return null;
  }

  return <>{children}</>;
}
