# TradingAgents Frontend Dockerfile - 本地构建版本
# 适用于网络受限环境

# 使用本地已有的Node.js镜像，如果没有则需要先下载
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制整个项目
COPY .. .

# 安装依赖并构建
RUN npm ci && \
    npm run build && \
    npm ci --only=production && \
    npm cache clean --force

# 安装系统依赖
RUN apk add --no-cache wget curl

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 设置文件权限
RUN chown -R nextjs:nodejs /app

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

# 启动应用
CMD ["npm", "start"]
