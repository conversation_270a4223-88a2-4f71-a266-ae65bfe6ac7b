# TradingAgents Frontend Development Dockerfile
# 开发模式，支持热重载

FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    wget \
    curl \
    git

# 复制 package 文件
COPY ../package*.json ./
COPY ../yarn.lock* ./

# 安装依赖
RUN npm install

# 复制源代码
COPY .. .

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev"]
