# 生产环境配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# 前端配置
NEXT_PUBLIC_API_BASE_URL=http://your-server-ip:5000
NEXT_PUBLIC_WS_URL=ws://your-server-ip:8000
NEXT_PUBLIC_OPENAI_API_KEY=your-openai-api-key
NEXT_PUBLIC_FINNHUB_API_KEY=your-finnhub-api-key
BACK_END_URL=http://your-server-ip:5000

# 数据库配置
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_agents
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123

# 注意事项:
# 1. 将 your-server-ip 替换为您的服务器实际IP地址
# 2. 将 your-openai-api-key 替换为您的 OpenAI API 密钥
# 3. 将 your-finnhub-api-key 替换为您的 Finnhub API 密钥
# 4. 生产环境建议修改默认密码
# 5. 如果使用域名，请将IP地址替换为域名
