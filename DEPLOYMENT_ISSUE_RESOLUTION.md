# 部署问题解决方案

## 问题现象

从你提供的部署日志可以看到：
```
Couldn't find env file: /***/.env.production
```

这个错误在以下步骤中重复出现：
- 拉取镜像时
- 停止服务时  
- 启动服务时

## 根本原因

1. **路径不一致问题**
   - CI/CD中使用了不同的路径变量
   - 健康检查使用 `/root`，部署使用 `${{ secrets.DEPLOY_PATH }}`

2. **SSH HERE文档变量替换问题**
   - 使用了单引号的HERE文档 `<< 'EOF'`
   - 导致GitHub Actions变量无法正确替换

3. **文件检查缺失**
   - 没有验证文件是否成功上传到服务器

## 修复措施

### ✅ 1. 修复CI/CD配置

**修复前：**
```yaml
ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << 'EOF'
  cd ${{ secrets.DEPLOY_PATH }}  # 变量无法替换
```

**修复后：**
```yaml
ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << EOF
  cd ${{ secrets.DEPLOY_PATH }}  # 变量可以正确替换
```

### ✅ 2. 添加文件存在性检查

```bash
echo "1. 检查文件是否存在..."
if [ ! -f .env.production ]; then
  echo "❌ .env.production 文件不存在"
  exit 1
fi
if [ ! -f docker-compose.prod.frontend.yml ]; then
  echo "❌ docker-compose.prod.frontend.yml 文件不存在"
  exit 1
fi
```

### ✅ 3. 统一路径使用

所有操作都使用 `${{ secrets.DEPLOY_PATH }}`：
- 文件上传
- SSH操作
- 健康检查

### ✅ 4. 增强错误处理

```bash
# 停止服务时允许失败（如果服务未运行）
docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production stop frontend || true
```

## 验证步骤

### 1. 检查本地文件

确保以下文件存在：
```bash
ls -la .env.production
ls -la docker/docker-compose.prod.frontend.yml
```

### 2. 验证GitHub Secrets

确保以下Secrets已配置：
- `DEPLOY_HOST` - 服务器IP
- `DEPLOY_USER` - 服务器用户名
- `DEPLOY_PATH` - 部署路径
- `DEPLOY_SSH_KEY` - SSH私钥
- `ALIYUN_REGISTRY_USERNAME` - 阿里云用户名
- `ALIYUN_REGISTRY_PASSWORD` - 阿里云密码
- 其他API相关secrets

### 3. 测试部署配置

使用测试脚本验证配置：
```bash
export DEPLOY_HOST="your-server-ip"
export DEPLOY_USER="your-username"  
export DEPLOY_PATH="/your/deploy/path"
chmod +x scripts/test-deployment.sh
./scripts/test-deployment.sh
```

## 下一步操作

1. **提交修复**：
   ```bash
   git add .
   git commit -m "fix: 修复部署配置中的路径和变量替换问题"
   git push origin main
   ```

2. **监控部署**：
   - 在GitHub Actions中查看部署日志
   - 确认不再出现 "Couldn't find env file" 错误

3. **验证服务**：
   - 检查容器是否正常启动
   - 访问应用确认功能正常

## 预期结果

修复后的部署日志应该显示：
```
1. 检查文件是否存在...
✅ 文件检查通过
2. 登录阿里云镜像服务...
Login Succeeded
3. 拉取最新前端镜像...
✅ 镜像拉取成功
4. 停止旧前端服务...
✅ 服务停止成功
5. 启动新前端服务...
✅ 服务启动成功
```

## 故障排除

如果仍然遇到问题：

1. **检查文件上传**：
   ```bash
   ssh user@server "ls -la /deploy/path/"
   ```

2. **检查文件内容**：
   ```bash
   ssh user@server "cat /deploy/path/.env.production"
   ```

3. **手动测试Docker Compose**：
   ```bash
   ssh user@server "cd /deploy/path && docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production config"
   ```

这些修复应该能够解决你遇到的部署问题。
