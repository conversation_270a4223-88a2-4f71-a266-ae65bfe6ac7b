# 部署工作流
name: Deploy Application

on:
  workflow_run:
    workflows: ['CI/CD Pipeline']
    types: [completed]
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: '强制部署（跳过检查）'
        required: false
        default: false
        type: boolean

env:
  ALIYUN_REGISTRY: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
  ALIYUN_NAMESPACE: ez_trading
  ALIYUN_IMAGE_NAME: frontend
  # 部署环境URL（从 GitHub Secrets 获取）
  DEPLOY_URL: ${{ secrets.DEPLOY_URL }}
  API_URL: ${{ secrets.API_URL }}

jobs:
  # 部署到生产环境
  deploy:
    name: 部署应用
    runs-on: ubuntu-latest
    if: |
      (github.event.workflow_run.conclusion == 'success' && (github.event.workflow_run.head_branch == 'main' || github.event.workflow_run.head_branch == 'develop')) ||
      (github.event_name == 'workflow_dispatch')
    environment:
      name: production
      url: ${{ env.DEPLOY_URL }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置部署环境变量
        run: |
          echo "DEPLOY_ENV=production" >> $GITHUB_ENV
          echo "IMAGE_TAG=latest" >> $GITHUB_ENV
          echo "COMPOSE_FILE=docker/docker-compose.prod.yml" >> $GITHUB_ENV

      - name: 验证镜像存在
        run: |
          echo "验证镜像 ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }} 是否存在..."
          # 这里可以添加镜像验证逻辑

      - name: 准备部署配置
        run: |
          # 创建环境配置文件
          cat > .env.production << EOF
          NODE_ENV=production
          NEXT_PUBLIC_API_BASE_URL=${{ env.API_URL }}
          NEXT_PUBLIC_WS_URL=${{ secrets.NEXT_PUBLIC_WS_URL }}
          NEXT_PUBLIC_OPENAI_API_KEY=${{ secrets.NEXT_PUBLIC_OPENAI_API_KEY }}
          NEXT_PUBLIC_FINNHUB_API_KEY=${{ secrets.NEXT_PUBLIC_FINNHUB_API_KEY }}
          BACK_END_URL=${{ env.API_URL }}
          MYSQL_ROOT_PASSWORD=${{ secrets.MYSQL_ROOT_PASSWORD }}
          MYSQL_DATABASE=trading_analysis
          MYSQL_USER=trading_user
          MYSQL_PASSWORD=${{ secrets.MYSQL_PASSWORD }}
          EOF

      - name: 数据库迁移检查
        run: |
          echo "检查数据库迁移..."
          # 这里添加数据库迁移检查逻辑
          # 例如：检查是否有新的迁移文件需要执行

      - name: 设置SSH密钥
        run: |
          # 创建SSH目录
          mkdir -p ~/.ssh

          # 添加SSH私钥
          echo "${{ secrets.DEPLOY_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key

          # 添加服务器到known_hosts
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 部署应用到服务器
        run: |
          echo "开始部署应用到服务器..."

          # 上传配置文件到服务器
          scp -i ~/.ssh/deploy_key .env.production ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:${{ secrets.DEPLOY_PATH }}/
          scp -i ~/.ssh/deploy_key ${{ env.COMPOSE_FILE }} ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:${{ secrets.DEPLOY_PATH }}/

          # SSH连接到服务器执行部署
          ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << 'EOF'
            cd ${{ secrets.DEPLOY_PATH }}

            echo "1. 登录阿里云镜像服务..."
            echo "${{ secrets.ALIYUN_REGISTRY_PASSWORD }}" | docker login --username ${{ secrets.ALIYUN_REGISTRY_USERNAME }} --password-stdin ${{ env.ALIYUN_REGISTRY }}

            echo "2. 拉取最新镜像..."
            docker-compose -f ${{ env.COMPOSE_FILE }} --env-file .env.production pull

            echo "3. 停止旧服务..."
            docker-compose -f ${{ env.COMPOSE_FILE }} --env-file .env.production down

            echo "4. 启动新服务..."
            docker-compose -f ${{ env.COMPOSE_FILE }} --env-file .env.production up -d

            echo "5. 清理旧镜像..."
            docker image prune -f
          EOF

          echo "部署完成"

      - name: 健康检查
        run: |
          echo "执行健康检查..."

          # 等待服务启动
          sleep 30

          # 检查服务是否正常运行
          MAX_RETRIES=10
          RETRY_COUNT=0

          while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
            # 通过SSH检查服务器上的容器状态
            CONTAINER_STATUS=$(ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f ${{ env.COMPOSE_FILE }} ps --services --filter status=running | wc -l")

            if [ "$CONTAINER_STATUS" -gt 0 ]; then
              echo "✅ 容器运行状态检查通过"

              # 检查HTTP健康端点
              if curl -f -s ${{ env.DEPLOY_URL }}/api/health > /dev/null 2>&1; then
                echo "✅ 应用健康检查通过"
                break
              else
                echo "⚠️ HTTP健康检查失败，但容器正在运行"
              fi
            else
              echo "⏳ 等待服务启动... ($((RETRY_COUNT + 1))/$MAX_RETRIES)"
            fi

            sleep 15
            RETRY_COUNT=$((RETRY_COUNT + 1))
          done

          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "❌ 健康检查失败"
            # 显示容器日志用于调试
            ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f ${{ env.COMPOSE_FILE }} logs --tail=50"
            exit 1
          fi

      - name: 运行集成测试
        run: |
          echo "运行集成测试..."
          # 这里可以添加集成测试逻辑

      - name: 通知部署结果
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ 应用部署成功"
            echo "🔗 访问地址: ${{ env.DEPLOY_URL }}"
          else
            echo "❌ 应用部署失败"
          fi
