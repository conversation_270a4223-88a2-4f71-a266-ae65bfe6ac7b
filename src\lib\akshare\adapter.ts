import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface PendingRequest {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
  timeout: NodeJS.Timeout;
}

class AKShareAdapter {
  private process: ChildProcess | null = null;
  private pythonPath: string;
  private scriptPath: string;
  private pendingRequests: Map<string, PendingRequest> = new Map();

  constructor() {
    // 在实际应用中，Python 解释器的路径和脚本路径可能需要更灵活的配置方式
    this.pythonPath = 'python'; // 假设 'python' 在系统 PATH 中
    this.scriptPath = path.join(process.cwd(), 'src', 'python', 'akshare_worker.py');
  }

  public start(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log(`Starting Python worker from: ${this.scriptPath}`);
      this.process = spawn(this.pythonPath, ['-u', this.scriptPath]); // '-u' for unbuffered stdout

      this.process.on('spawn', () => {
        console.log('Python worker started successfully.');
        resolve();
      });

      this.process.on('error', (err) => {
        console.error('Failed to start Python worker:', err);
        reject(err);
      });

      this.process.stdout?.on('data', (data) => {
        const lines = data.toString().split('\n').filter(Boolean);
        for (const line of lines) {
          this.handleResponse(line);
        }
      });

      this.process.stderr?.on('data', (data) => {
        console.error(`[Python Worker Error]: ${data.toString()}`);
      });

      this.process.on('close', (code) => {
        console.log(`Python worker exited with code ${code}`);
        this.process = null;
      });
    });
  }

  public stop(): void {
    if (this.process) {
      console.log('Stopping Python worker...');
      this.pendingRequests.forEach((request) => {
        clearTimeout(request.timeout);
        request.reject(new Error('AKShareAdapter is stopping.'));
      });
      this.pendingRequests.clear();
      this.process.kill('SIGTERM'); // Send termination signal
      this.process = null;
    }
  }

  public invoke<T>(command: string, params: any = {}, timeout: number = 30000): Promise<T> {
    if (!this.process) {
      return Promise.reject(new Error('Python worker is not running.'));
    }

    const requestId = uuidv4();
    const request = {
      request_id: requestId,
      command,
      params,
    };

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error(`Request timed out after ${timeout / 1000}s`));
      }, timeout);

      this.pendingRequests.set(requestId, { resolve, reject, timeout: timeoutId });

      this.process?.stdin?.write(JSON.stringify(request) + '\n', (error) => {
        if (error) {
          clearTimeout(timeoutId);
          this.pendingRequests.delete(requestId);
          reject(error);
        }
      });
    });
  }

  private handleResponse(line: string): void {
    try {
      const response = JSON.parse(line);
      const { request_id, data, error } = response;

      if (!request_id || !this.pendingRequests.has(request_id)) {
        console.warn('Received response for unknown request:', line);
        return;
      }

      const pending = this.pendingRequests.get(request_id)!;
      clearTimeout(pending.timeout);
      this.pendingRequests.delete(request_id);

      if (error) {
        console.error('Python worker returned an error:', error);
        pending.reject(new Error(error.message || error));
      } else {
        pending.resolve(data);
      }
    } catch (e) {
      console.error('Failed to parse response from Python worker:', line, e);
    }
  }
}

export const akshareAdapter = new AKShareAdapter();
