#!/bin/bash

# 部署调试脚本
# 帮助诊断文件上传和部署问题

set -e

echo "🔍 开始部署调试..."

# 检查环境变量
echo "1. 检查环境变量..."
echo "DEPLOY_HOST: ${DEPLOY_HOST:-未设置}"
echo "DEPLOY_USER: ${DEPLOY_USER:-未设置}"
echo "DEPLOY_PATH: ${DEPLOY_PATH:-未设置}"

if [ -z "$DEPLOY_HOST" ] || [ -z "$DEPLOY_USER" ] || [ -z "$DEPLOY_PATH" ]; then
    echo "❌ 请设置必要的环境变量"
    echo "export DEPLOY_HOST='your-server-ip'"
    echo "export DEPLOY_USER='your-username'"
    echo "export DEPLOY_PATH='/your/deploy/path'"
    exit 1
fi

# 检查本地文件
echo ""
echo "2. 检查本地文件..."
if [ -f .env.production ]; then
    echo "✅ .env.production 存在 ($(wc -c < .env.production) bytes)"
    echo "内容预览:"
    head -5 .env.production
else
    echo "❌ .env.production 不存在"
fi

if [ -f docker/docker-compose.prod.frontend.yml ]; then
    echo "✅ docker-compose.prod.frontend.yml 存在 ($(wc -c < docker/docker-compose.prod.frontend.yml) bytes)"
else
    echo "❌ docker-compose.prod.frontend.yml 不存在"
fi

# 检查SSH连接
echo ""
echo "3. 测试SSH连接..."
if [ ! -f ~/.ssh/deploy_key ]; then
    echo "❌ SSH密钥不存在: ~/.ssh/deploy_key"
    echo "请确保SSH密钥已正确配置"
    exit 1
fi

if ssh -i ~/.ssh/deploy_key -o ConnectTimeout=10 $DEPLOY_USER@$DEPLOY_HOST "echo 'SSH连接成功'"; then
    echo "✅ SSH连接正常"
else
    echo "❌ SSH连接失败"
    exit 1
fi

# 测试文件上传
echo ""
echo "4. 测试文件上传..."
if [ -f .env.production ]; then
    echo "测试上传 .env.production..."
    if scp -i ~/.ssh/deploy_key .env.production $DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PATH/test.env.production; then
        echo "✅ .env.production 上传成功"
        # 清理测试文件
        ssh -i ~/.ssh/deploy_key $DEPLOY_USER@$DEPLOY_HOST "rm -f $DEPLOY_PATH/test.env.production"
    else
        echo "❌ .env.production 上传失败"
    fi
fi

if [ -f docker/docker-compose.prod.frontend.yml ]; then
    echo "测试上传 docker-compose.prod.frontend.yml..."
    if scp -i ~/.ssh/deploy_key docker/docker-compose.prod.frontend.yml $DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PATH/test.docker-compose.yml; then
        echo "✅ docker-compose.prod.frontend.yml 上传成功"
        # 清理测试文件
        ssh -i ~/.ssh/deploy_key $DEPLOY_USER@$DEPLOY_HOST "rm -f $DEPLOY_PATH/test.docker-compose.yml"
    else
        echo "❌ docker-compose.prod.frontend.yml 上传失败"
    fi
fi

# 检查服务器目录权限
echo ""
echo "5. 检查服务器目录..."
ssh -i ~/.ssh/deploy_key $DEPLOY_USER@$DEPLOY_HOST << EOF
    echo "目标目录: $DEPLOY_PATH"
    echo "目录权限:"
    ls -ld $DEPLOY_PATH
    echo ""
    echo "目录内容:"
    ls -la $DEPLOY_PATH
    echo ""
    echo "磁盘空间:"
    df -h $DEPLOY_PATH
EOF

echo ""
echo "🎉 调试完成！"
echo ""
echo "如果所有测试都通过，说明配置正确。"
echo "如果有失败的测试，请根据错误信息进行修复。"
