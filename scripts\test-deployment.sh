#!/bin/bash

# 测试部署配置脚本
# 验证服务器上的文件和配置是否正确

set -e

echo "🔍 测试部署配置..."

# 检查必要的环境变量
if [ -z "$DEPLOY_HOST" ] || [ -z "$DEPLOY_USER" ] || [ -z "$DEPLOY_PATH" ]; then
    echo "❌ 缺少必要的环境变量"
    echo "请设置: DEPLOY_HOST, DEPLOY_USER, DEPLOY_PATH"
    echo "例如:"
    echo "export DEPLOY_HOST='*************'"
    echo "export DEPLOY_USER='root'"
    echo "export DEPLOY_PATH='/root'"
    exit 1
fi

# 检查SSH密钥
if [ ! -f ~/.ssh/deploy_key ]; then
    echo "❌ SSH密钥不存在: ~/.ssh/deploy_key"
    echo "请确保SSH密钥已正确配置"
    exit 1
fi

echo "1. 测试SSH连接..."
if ssh -i ~/.ssh/deploy_key -o ConnectTimeout=10 $DEPLOY_USER@$DEPLOY_HOST "echo 'SSH连接成功'"; then
    echo "✅ SSH连接正常"
else
    echo "❌ SSH连接失败"
    exit 1
fi

echo "2. 检查服务器上的文件..."
ssh -i ~/.ssh/deploy_key $DEPLOY_USER@$DEPLOY_HOST << EOF
    cd $DEPLOY_PATH
    
    echo "检查当前目录: \$(pwd)"
    echo "目录内容:"
    ls -la
    
    echo ""
    echo "检查必要文件:"
    
    if [ -f .env.production ]; then
        echo "✅ .env.production 存在"
        echo "文件大小: \$(wc -c < .env.production) bytes"
    else
        echo "❌ .env.production 不存在"
    fi
    
    if [ -f docker-compose.prod.frontend.yml ]; then
        echo "✅ docker-compose.prod.frontend.yml 存在"
        echo "文件大小: \$(wc -c < docker-compose.prod.frontend.yml) bytes"
    else
        echo "❌ docker-compose.prod.frontend.yml 不存在"
    fi
EOF

echo "3. 测试Docker Compose配置..."
ssh -i ~/.ssh/deploy_key $DEPLOY_USER@$DEPLOY_HOST << EOF
    cd $DEPLOY_PATH
    
    if [ -f .env.production ] && [ -f docker-compose.prod.frontend.yml ]; then
        echo "测试Docker Compose配置验证..."
        if docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production config > /dev/null 2>&1; then
            echo "✅ Docker Compose配置验证通过"
        else
            echo "❌ Docker Compose配置验证失败"
            echo "错误详情:"
            docker-compose -f docker-compose.prod.frontend.yml --env-file .env.production config
        fi
    else
        echo "⚠️ 跳过Docker Compose测试 - 缺少必要文件"
    fi
EOF

echo "4. 检查Docker服务状态..."
ssh -i ~/.ssh/deploy_key $DEPLOY_USER@$DEPLOY_HOST << EOF
    echo "Docker版本:"
    docker --version
    
    echo ""
    echo "Docker Compose版本:"
    docker-compose --version
    
    echo ""
    echo "当前运行的容器:"
    docker ps
EOF

echo ""
echo "🎉 部署配置测试完成！"
echo ""
echo "如果所有检查都通过，你可以安全地进行部署。"
echo "如果有任何问题，请根据上面的输出进行修复。"
