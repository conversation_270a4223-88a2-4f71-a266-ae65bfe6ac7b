# 数据库保护部署指南

## 🚨 问题描述

当你有多个Docker服务（如前端和数据库）时，部署前端可能会意外影响到数据库服务，导致：
- 数据库容器被删除
- 数据丢失
- 服务中断

## 🛡️ 保护策略

### 1. 分离部署配置

**推荐做法：**
- 前端使用：`docker-compose.prod.frontend.yml`
- 数据库使用：`docker-compose.prod.backend.yml` 或单独部署

**避免使用：**
- 包含所有服务的单一compose文件进行部署

### 2. 避免危险的清理命令

**❌ 危险命令（会影响所有容器）：**
```bash
docker container prune -f        # 删除所有停止的容器
docker system prune -f           # 清理整个Docker系统
docker-compose down --remove-orphans  # 可能删除其他项目的容器
```

**✅ 安全命令（只影响特定服务）：**
```bash
docker-compose stop frontend     # 只停止前端服务
docker-compose rm -f frontend    # 只删除前端容器
docker container ls -a --filter "name=frontend" --format "{{.ID}}" | xargs -r docker rm -f
```

### 3. 精确的镜像清理

**❌ 危险：**
```bash
docker image prune -f            # 删除所有未使用的镜像
```

**✅ 安全：**
```bash
docker images --filter "reference=*frontend*" --format "{{.ID}}" | xargs -r docker rmi -f
```

## 🔧 已实施的保护措施

我已经修改了CI/CD配置，实施以下保护措施：

### 1. 精确的服务控制
```yaml
# 只停止前端服务
docker-compose -f docker-compose.prod.frontend.yml stop frontend
docker-compose -f docker-compose.prod.frontend.yml rm -f frontend
```

### 2. 容器级别的清理
```yaml
# 只清理前端容器
docker container ls -a --filter "name=tradingagents-frontend" --format "{{.ID}}" | xargs -r docker rm -f
```

### 3. 镜像级别的清理
```yaml
# 只清理前端镜像
docker images --filter "reference=*frontend*" --format "{{.ID}}" | xargs -r docker rmi -f
```

### 4. 移除危险的全局清理
- 移除了 `docker container prune -f`
- 移除了 `docker system prune -f`
- 移除了 `--remove-orphans` 标志

## 📋 最佳实践

### 1. 数据库独立部署
```bash
# 数据库单独部署和管理
docker-compose -f docker-compose.prod.backend.yml up -d mysql
```

### 2. 使用Docker网络隔离
```yaml
# 在compose文件中使用外部网络
networks:
  tradingagents-network:
    external: true
```

### 3. 数据持久化
```yaml
# 确保数据库数据持久化
volumes:
  mysql_data:
    driver: local
```

### 4. 备份策略
```bash
# 定期备份数据库
docker exec mysql-container mysqldump -u root -p database_name > backup.sql
```

## 🚀 部署流程

### 安全的部署顺序：

1. **首次部署数据库**：
```bash
docker-compose -f docker-compose.prod.backend.yml up -d mysql
```

2. **部署前端**（通过CI/CD）：
```bash
git push origin main  # 触发前端部署
```

3. **验证服务**：
```bash
docker ps  # 确认所有服务都在运行
```

## 🔍 故障排除

### 如果数据库被意外删除：

1. **检查数据卷**：
```bash
docker volume ls
```

2. **恢复数据库**：
```bash
# 如果数据卷还在
docker-compose -f docker-compose.prod.backend.yml up -d mysql

# 如果需要从备份恢复
docker exec -i mysql-container mysql -u root -p database_name < backup.sql
```

3. **重新连接服务**：
```bash
docker-compose -f docker-compose.prod.frontend.yml restart frontend
```

## ⚠️ 监控建议

1. **定期检查容器状态**：
```bash
docker ps -a
```

2. **监控数据卷**：
```bash
docker volume ls
docker volume inspect mysql_data
```

3. **设置告警**：
- 监控数据库容器状态
- 监控数据卷使用情况
- 设置自动备份

现在你的数据库应该受到保护，不会在前端部署时被意外影响！
