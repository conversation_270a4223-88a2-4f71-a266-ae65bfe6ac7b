import { NextResponse } from 'next/server';
import { serverApiMethods } from '@/lib/server-api';

export async function GET() {
  try {
    // 检查应用健康状态
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      services: {
        frontend: 'healthy',
        api: await checkApiHealth(),
      },
    };

    return NextResponse.json(healthData, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

async function checkApiHealth(): Promise<string> {
  try {
    const healthResult = await serverApiMethods.healthCheck();
    return healthResult.status;
  } catch {
    return 'unhealthy';
  }
}
