'use client';

import AnimatedMultiSelect from '@/components/ui/AnimatedMultiSelect';
import AnimatedSelect from '@/components/ui/AnimatedSelect';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StockBackgroundElements } from '@/components/ui/StockBackgroundElements';
import { AnalysisPeriod, analysisPeriodOptions as apiAnalysisPeriodOptions, researchDepthOptions as apiResearchDepthOptions, ResearchDepth } from '@/types/database';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

export default function CreateTaskPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [ticker, setTicker] = useState('');
  const [analysisPeriod, setAnalysisPeriod] = useState('');
  const [selectedAnalysts, setSelectedAnalysts] = useState<string[]>([]);
  const [researchDepth, setResearchDepth] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取当前用户信息
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          const data = await response.json();
          setUser(data.user);
        }
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  // 分析师团队选项 - 多选支持
  const analystOptions = [
    { value: 'market', label: '市场分析师' },
    { value: 'social', label: '社交媒体分析师' },
    { value: 'news', label: '新闻分析师' },
    { value: 'fundamentals', label: '基本面分析师' },
    { value: 'technical', label: '技术分析师' },
    { value: 'risk', label: '风险分析师' },
  ];

  // 分析周期选项 - 根据HTML设计简化
  const analysisPeriodOptions = [
    { value: '1d', label: '1天' },
    { value: '1w', label: '1周' },
    { value: '1m', label: '1个月' },
    { value: '3m', label: '3个月' },
    { value: '6m', label: '6个月' },
    { value: '1y', label: '1年' },
  ].filter(option => apiAnalysisPeriodOptions.includes(option.value as AnalysisPeriod));

  // 研究深度选项 - 根据HTML设计简化
  const researchDepthOptions = [
    { value: 'shallow', label: '浅层分析' },
    { value: 'medium', label: '中等分析' },
    { value: 'deep', label: '深度分析' },
  ].filter(option => apiResearchDepthOptions.includes(option.value as ResearchDepth));

  const handleSubmit = async () => {
    if (!ticker || !analysisPeriod || selectedAnalysts.length === 0 || !researchDepth) {
      toast.error('请填写所有必填字段');
      return;
    }

    setIsSubmitting(true);

    try {
      const config = {
        ticker: ticker.toUpperCase(),
        analysisPeriod,
        selectedAnalysts,
        researchDepth,
        timestamp: new Date().toISOString(),
        userId: user.id, // 添加用户ID
      };

      const response = await fetch('/api/langgraph/analysis/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        toast.success('任务创建成功！');
        router.push('/tasks');
      } else {
        const error = await response.json();
        toast.error(error.error || '创建任务失败');
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#131520]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#131520] dark group/design-root overflow-x-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* 动态渐变背景 */}
        <div className="absolute inset-0 gradient-bg"></div>

        {/* 几何装饰 - 添加动画 */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2 animate-float"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-tl from-indigo-500/10 to-cyan-500/10 rounded-full blur-3xl transform translate-x-1/2 translate-y-1/2 animate-glow"></div>

        {/* 旋转光环 */}
        <div className="absolute top-1/2 left-1/2 w-64 h-64 border border-blue-500/10 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-rotate-slow"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 border border-purple-500/10 rounded-full transform -translate-x-1/2 -translate-y-1/2 animate-rotate-slow" style={{animationDirection: 'reverse'}}></div>

        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
          backgroundSize: '20px 20px'
        }}></div>

        {/* 动画粒子 */}
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>

        {/* 股票相关背景元素 */}
        <StockBackgroundElements />

        {/* 额外的浮动元素 */}
        <div className="absolute top-20 right-20 w-3 h-3 bg-blue-400/20 rounded-full animate-pulse-slow"></div>
        <div className="absolute bottom-32 left-16 w-2 h-2 bg-purple-400/25 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/3 left-1/3 w-1 h-1 bg-cyan-400/30 rounded-full animate-glow" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="layout-container flex h-full grow flex-col relative z-10">
        {/* 主内容区域 - 垂直居中 */}
        <div className="flex flex-1 items-center justify-center py-8">
          <div className="w-full max-w-[600px] mx-auto px-8">
            {/* 内容卡片背景 - 添加悬浮和光晕效果 */}
            <div className="relative bg-[#1a1f2e]/60 backdrop-blur-md rounded-2xl border border-[#2a2f47]/60 shadow-2xl p-8 card-hover glow-effect">
              {/* 内部光晕 */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 rounded-2xl"></div>
              <div className="relative z-10">
                {/* 页面标题 */}
                <div className="text-center mb-8">
                  <p className="text-white text-[32px] font-bold leading-tight mb-2">创建新的分析任务</p>
                  <p className="text-[#99a0c2] text-base">欢迎 {user?.username}，配置您的股票分析参数</p>
                </div>

                {/* 表单容器 */}
                <div className="space-y-6">
                  {/* 股票代码输入 */}
                  <div className="space-y-2">
                    <label className="block">
                      <p className="text-white text-base font-medium leading-normal pb-2">股票代码</p>
                      <div className="flex w-full items-stretch rounded-xl">
                        <input
                          placeholder="请输入股票代码"
                          className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-white focus:outline-0 focus:ring-0 border-none bg-[#282d43] focus:border-none h-14 placeholder:text-[#99a0c2] p-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                          value={ticker}
                          onChange={(e) => setTicker(e.target.value.toUpperCase())}
                        />
                        <div className="text-[#99a0c2] flex border-none bg-[#282d43] items-center justify-center pr-4 rounded-r-xl border-l-0">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M224,88H175.4l8.47-46.57a8,8,0,0,0-15.74-2.86l-9,49.43H111.4l8.47-46.57a8,8,0,0,0-15.74-2.86L95.14,88H48a8,8,0,0,0,0,16H92.23L83.5,152H32a8,8,0,0,0,0,16H80.6l-8.47,46.57a8,8,0,0,0,6.44,9.3A7.79,7.79,0,0,0,80,224a8,8,0,0,0,7.86-6.57l9-49.43H144.6l-8.47,46.57a8,8,0,0,0,6.44,9.3A7.79,7.79,0,0,0,144,224a8,8,0,0,0,7.86-6.57l9-49.43H208a8,8,0,0,0,0-16H163.77l8.73-48H224a8,8,0,0,0,0-16Zm-76.5,64H99.77l8.73-48h47.73Z"></path>
                          </svg>
                        </div>
                      </div>
                    </label>
                  </div>

                  {/* 分析周期选择 - 动画下拉框 */}
                  <AnimatedSelect
                    label="分析周期"
                    options={analysisPeriodOptions}
                    value={analysisPeriod}
                    onChange={setAnalysisPeriod}
                    placeholder="请选择分析周期"
                  />

                  {/* 分析师团队选择 - 多选动画下拉框 */}
                  <AnimatedMultiSelect
                    label="分析师团队"
                    options={analystOptions}
                    selectedValues={selectedAnalysts}
                    onChange={setSelectedAnalysts}
                    placeholder="请选择分析师团队"
                  />

                  {/* 研究深度选择 - 动画下拉框 */}
                  <AnimatedSelect
                    label="研究深度"
                    options={researchDepthOptions}
                    value={researchDepth}
                    onChange={setResearchDepth}
                    placeholder="请选择研究深度"
                  />

                  {/* 提交按钮 */}
                <div className="pt-4">
                  {user ? (
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      className="w-full flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-6 bg-[#eaedfa] text-[#131520] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#d4d9f0] transition-colors"
                    >
                      <span className="truncate">
                        {isSubmitting ? '创建中...' : '创建任务'}
                      </span>
                    </button>
                  ) : (
                    <div className="text-center">
                      <p className="text-[#99a0c2] text-sm mb-4">请先登录以创建分析任务</p>
                      <div className="flex space-x-4 justify-center">
                        <button
                          onClick={() => router.push('/login')}
                          className="flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-6 bg-[#eaedfa] text-[#131520] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d4d9f0] transition-colors"
                        >
                          登录
                        </button>
                        <button
                          onClick={() => router.push('/register')}
                          className="flex cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-6 bg-[#282d43] text-white text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#3a3f55] transition-colors"
                        >
                          注册
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
}
