#!/bin/bash

# 快速 HTTPS 配置脚本 - 适用于已有证书的情况
set -e

echo "=== 快速 HTTPS 配置 ==="
echo

# 获取域名
read -p "请输入您的域名: " DOMAIN
if [ -z "$DOMAIN" ]; then
    echo "域名不能为空"
    exit 1
fi

echo "选择证书配置方式:"
echo "1. 我已有SSL证书文件"
echo "2. 使用 Let's Encrypt 自动获取证书"
echo "3. 暂时使用自签名证书 (仅测试用)"

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "请将您的证书文件放置到以下位置:"
        echo "  ssl/fullchain.pem  (完整证书链)"
        echo "  ssl/privkey.pem    (私钥文件)"
        read -p "证书文件已准备好了吗? (y/N): " ready
        if [[ ! $ready =~ ^[Yy]$ ]]; then
            echo "请准备好证书文件后重新运行脚本"
            exit 0
        fi
        ;;
    2)
        echo "使用 Let's Encrypt 获取证书..."
        read -p "请输入您的邮箱: " EMAIL
        if [ -z "$EMAIL" ]; then
            echo "邮箱不能为空"
            exit 1
        fi
        
        # 创建目录
        mkdir -p certbot/conf certbot/www ssl
        
        # 获取证书
        docker run --rm \
            -p 80:80 \
            -v $(pwd)/certbot/conf:/etc/letsencrypt \
            -v $(pwd)/certbot/www:/var/www/certbot \
            certbot/certbot certonly \
            --standalone \
            --email $EMAIL \
            --agree-tos \
            --no-eff-email \
            -d $DOMAIN
        
        # 复制证书
        cp certbot/conf/live/$DOMAIN/fullchain.pem ssl/
        cp certbot/conf/live/$DOMAIN/privkey.pem ssl/
        ;;
    3)
        echo "生成自签名证书 (仅用于测试)..."
        mkdir -p ssl
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout ssl/privkey.pem \
            -out ssl/fullchain.pem \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=$DOMAIN"
        echo "⚠️  自签名证书仅用于测试，浏览器会显示安全警告"
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

# 创建配置文件
echo "配置 Nginx..."
sed "s/your-domain.com/$DOMAIN/g" nginx-https.conf > nginx-configured.conf

# 创建环境变量
echo "创建环境变量文件..."
cat > .env << EOF
DOMAIN=$DOMAIN
NEXT_PUBLIC_API_BASE_URL=https://$DOMAIN
NEXT_PUBLIC_WS_URL=wss://$DOMAIN/ws
BACK_END_URL=http://backend:8000
MYSQL_ROOT_PASSWORD=trading123
MYSQL_DATABASE=trading_agents
MYSQL_USER=trading_user
MYSQL_PASSWORD=trading123
EOF

# 更新 docker-compose 配置
echo "更新 Docker Compose 配置..."
cp nginx-configured.conf nginx-https.conf

# 创建必要目录
mkdir -p mysql/init

# 启动服务
echo "启动 HTTPS 服务..."
docker-compose -f docker-compose.https.yml up -d

echo
echo "=== 配置完成! ==="
echo "您的网站现在可以通过 https://$DOMAIN 访问"
echo
echo "检查服务状态:"
docker-compose -f docker-compose.https.yml ps

if [ "$choice" = "2" ]; then
    echo
    echo "设置证书自动续期..."
    cat > renew-cert.sh << 'EOF'
#!/bin/bash
docker run --rm \
    -v $(pwd)/certbot/conf:/etc/letsencrypt \
    -v $(pwd)/certbot/www:/var/www/certbot \
    certbot/certbot renew --quiet
cp certbot/conf/live/*/fullchain.pem ssl/
cp certbot/conf/live/*/privkey.pem ssl/
docker-compose -f docker-compose.https.yml exec nginx nginx -s reload
EOF
    chmod +x renew-cert.sh
    echo "续期脚本已创建: renew-cert.sh"
fi
