import { akshareAdapter } from '@/lib/akshare/adapter';
import { TradingAgentState } from '@/lib/langgraph';
import { AKShareNewsItem } from '@/types'; // This type needs to be created

const POSITIVE_KEYWORDS = ['利好', '上涨', '盈利', '增长', '新高', '推荐', '买入'];
const NEGATIVE_KEYWORDS = ['利空', '下跌', '亏损', '下降', '新低', '风险', '卖出'];

function simpleSentimentAnalysis(text: string): {
  score: number;
  sentiment: 'positive' | 'negative' | 'neutral';
} {
  let score = 0;
  for (const keyword of POSITIVE_KEYWORDS) {
    if (text.includes(keyword)) score++;
  }
  for (const keyword of NEGATIVE_KEYWORDS) {
    if (text.includes(keyword)) score--;
  }

  if (score > 0) return { score, sentiment: 'positive' };
  if (score < 0) return { score, sentiment: 'negative' };
  return { score, sentiment: 'neutral' };
}

export async function sentimentAnalystNode(
  state: TradingAgentState
): Promise<Partial<TradingAgentState>> {
  const ticker = state.ticker;
  if (!ticker) {
    throw new Error('Ticker not found in state');
  }

  console.log(`[Sentiment Analyst] Analyzing ticker: ${ticker}`);

  try {
    const newsData = await akshareAdapter.invoke<AKShareNewsItem[]>('get_stock_news', {
      symbol: ticker,
    });

    if (!Array.isArray(newsData) || newsData.length === 0) {
      throw new Error('Failed to retrieve valid news data.');
    }

    let totalScore = 0;
    const analyzedArticles = newsData.map((article) => {
      const sentimentResult = simpleSentimentAnalysis(article.标题);
      totalScore += sentimentResult.score;
      return { ...article, sentiment: sentimentResult };
    });

    const overallSentiment = simpleSentimentAnalysis(analyzedArticles.map((a) => a.标题).join(' '));

    const analysis = `对 ${ticker} 的情绪分析：\n- 共分析 ${newsData.length} 条新闻。\n- 整体情绪倾向: ${overallSentiment.sentiment} (分数: ${totalScore})。`;

    const newMessages = [...state.messages, { type: 'ai', content: analysis }];
    const analysisResults = {
      ...state.analysisResults,
      sentiment: {
        summary: analysis,
        data: analyzedArticles,
        overallScore: totalScore,
      },
    };

    return { messages: newMessages, analysisResults };
  } catch (error) {
    console.error('[Sentiment Analyst] Error:', error);
    const errorMessage = `情绪分析失败: ${error instanceof Error ? error.message : String(error)}`;
    const newMessages = [...state.messages, { type: 'ai', content: errorMessage }];
    return { messages: newMessages };
  }
}
