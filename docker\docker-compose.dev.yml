services:
  # TradingAgents Frontend Development
  frontend-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: tradingagents-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
      - BACK_END_URL=${BACK_END_URL:-http://localhost:5000}
    volumes:
      # 挂载源代码以支持热重载
      - ../src:/app/src:ro
      - ../public:/app/public:ro
      - ../package.json:/app/package.json:ro
      - ../next.config.js:/app/next.config.js:ro
      - ../tailwind.config.js:/app/tailwind.config.js:ro
      - ../tsconfig.json:/app/tsconfig.json:ro
      # 排除 node_modules 以避免冲突
      - /app/node_modules
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network
