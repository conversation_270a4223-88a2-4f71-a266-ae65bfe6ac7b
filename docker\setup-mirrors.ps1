# Docker 国内镜像源配置脚本 (PowerShell)
# 适用于 Windows 系统

Write-Host "=== Docker 国内镜像加速器配置脚本 ===" -ForegroundColor Green

# 检查 Docker 是否安装
try {
    $dockerVersion = docker --version
    Write-Host "检测到 Docker: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未检测到 Docker，请先安装 Docker Desktop" -ForegroundColor Red
    exit 1
}

Write-Host "`n重要提示:" -ForegroundColor Yellow
Write-Host "推荐使用阿里云个人专属镜像加速器地址，速度更快且更稳定" -ForegroundColor Cyan
Write-Host "获取方式: 登录阿里云控制台 -> 容器镜像服务 -> 镜像工具 -> 镜像加速器" -ForegroundColor Cyan

# 获取当前脚本目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$daemonJsonPath = Join-Path $scriptDir "daemon.json"

# 检查 daemon.json 文件是否存在
if (-not (Test-Path $daemonJsonPath)) {
    Write-Host "错误: 找不到 daemon.json 文件" -ForegroundColor Red
    exit 1
}

Write-Host "`n配置选项:" -ForegroundColor Yellow
Write-Host "1. 自动配置 (推荐)" -ForegroundColor Cyan
Write-Host "2. 手动配置" -ForegroundColor Cyan
Write-Host "3. 查看当前配置" -ForegroundColor Cyan
Write-Host "4. 测试镜像源" -ForegroundColor Cyan

$choice = Read-Host "`n请选择操作 (1-4)"

switch ($choice) {
    "1" {
        Write-Host "`n开始自动配置..." -ForegroundColor Green
        
        # 读取 daemon.json 内容
        $daemonConfig = Get-Content $daemonJsonPath -Raw
        
        Write-Host "`n请按照以下步骤操作:" -ForegroundColor Yellow
        Write-Host "1. 获取阿里云专属镜像加速器地址:" -ForegroundColor White
        Write-Host "   - 登录 https://cr.console.aliyun.com" -ForegroundColor Gray
        Write-Host "   - 镜像工具 -> 镜像加速器 -> 复制加速器地址" -ForegroundColor Gray
        Write-Host "2. 打开 Docker Desktop" -ForegroundColor White
        Write-Host "3. 点击右上角的设置图标（齿轮）" -ForegroundColor White
        Write-Host "4. 选择 'Docker Engine'" -ForegroundColor White
        Write-Host "5. 将以下配置复制到配置框中:" -ForegroundColor White
        
        Write-Host "`n--- 配置内容开始 ---" -ForegroundColor Cyan
        Write-Host $daemonConfig -ForegroundColor Gray
        Write-Host "--- 配置内容结束 ---`n" -ForegroundColor Cyan

        Write-Host "6. 重要: 将配置中的 'https://xxx.mirror.aliyuncs.com' 替换为您的专属地址" -ForegroundColor Red
        Write-Host "7. 点击 'Apply & Restart' 重启 Docker" -ForegroundColor White
        
        # 将配置复制到剪贴板
        try {
            $daemonConfig | Set-Clipboard
            Write-Host "`n✓ 配置已复制到剪贴板，可直接粘贴" -ForegroundColor Green
        } catch {
            Write-Host "`n注意: 无法复制到剪贴板，请手动复制上述配置" -ForegroundColor Yellow
        }
    }
    
    "2" {
        Write-Host "`n手动配置说明:" -ForegroundColor Green
        Write-Host "请参考 '镜像源配置说明.md' 文件中的详细步骤" -ForegroundColor White
        
        # 打开说明文档
        $readmePath = Join-Path $scriptDir "镜像源配置说明.md"
        if (Test-Path $readmePath) {
            Start-Process notepad.exe $readmePath
        }
    }
    
    "3" {
        Write-Host "`n当前 Docker 配置:" -ForegroundColor Green
        try {
            docker info | Select-String "Registry Mirrors" -A 10
        } catch {
            Write-Host "无法获取 Docker 配置信息" -ForegroundColor Red
        }
    }
    
    "4" {
        Write-Host "`n测试镜像源..." -ForegroundColor Green
        
        # 测试拉取小镜像
        Write-Host "正在测试拉取 hello-world 镜像..." -ForegroundColor Yellow
        $startTime = Get-Date
        
        try {
            docker pull hello-world
            $endTime = Get-Date
            $duration = ($endTime - $startTime).TotalSeconds
            Write-Host "✓ 镜像拉取成功，耗时: $([math]::Round($duration, 2)) 秒" -ForegroundColor Green
        } catch {
            Write-Host "✗ 镜像拉取失败，请检查网络连接和镜像源配置" -ForegroundColor Red
        }
    }
    
    default {
        Write-Host "无效选择，请重新运行脚本" -ForegroundColor Red
    }
}

Write-Host "`n脚本执行完成" -ForegroundColor Green
Read-Host "按回车键退出"
