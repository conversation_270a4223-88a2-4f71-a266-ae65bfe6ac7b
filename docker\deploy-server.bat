@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === TradingAgents 服务器部署脚本 ===
echo.

:menu
echo 请选择操作:
echo 1. 完整部署 (登录 + 拉取镜像 + 启动服务)
echo 2. 完整部署 + Nginx (登录 + 拉取镜像 + 启动服务 + Nginx)
echo 3. 仅拉取镜像
echo 4. 仅启动服务
echo 5. 仅启动服务 + Nginx
echo 6. 停止服务
echo 7. 查看服务状态
echo 0. 退出
echo.

set /p choice="请输入选项 (0-7): "

if "%choice%"=="1" goto full_deploy
if "%choice%"=="2" goto full_deploy_nginx
if "%choice%"=="3" goto pull_only
if "%choice%"=="4" goto start_only
if "%choice%"=="5" goto start_nginx_only
if "%choice%"=="6" goto stop_only
if "%choice%"=="7" goto status_only
if "%choice%"=="0" goto end
echo 无效选项，请重新选择
goto menu

:check_docker
echo [INFO] 检查 Docker 环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装或未启动
    pause
    exit /b 1
)
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装
    pause
    exit /b 1
)
echo [INFO] Docker 环境检查通过
goto :eof

:login_aliyun
echo [INFO] 登录阿里云容器镜像服务...
echo 密码: ezreal123
docker login --username=aliyun1315382626 --password=ezreal123 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
if errorlevel 1 (
    echo [ERROR] 阿里云容器镜像服务登录失败
    pause
    exit /b 1
)
echo [INFO] 阿里云容器镜像服务登录成功
goto :eof

:pull_images
echo [INFO] 拉取最新镜像...
echo [INFO] 拉取前端镜像...
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
if errorlevel 1 (
    echo [ERROR] 前端镜像拉取失败
    pause
    exit /b 1
)

echo [INFO] 拉取数据库镜像...
docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28
if errorlevel 1 (
    echo [ERROR] 数据库镜像拉取失败
    pause
    exit /b 1
)

echo [INFO] 拉取 Nginx 镜像...
docker pull nginx:alpine
if errorlevel 1 (
    echo [ERROR] Nginx 镜像拉取失败
    pause
    exit /b 1
)

echo [INFO] 所有镜像拉取完成
goto :eof

:stop_containers
echo [INFO] 停止现有容器...
if exist "docker-compose.prod.yml" (
    docker-compose -f docker-compose.prod.yml down
) else (
    echo [WARNING] docker-compose.prod.yml 文件不存在，跳过停止容器步骤
)
goto :eof

:create_dirs
echo [INFO] 创建必要的目录...
if not exist "mysql" mkdir mysql
if not exist "mysql\init" mkdir mysql\init
goto :eof

:create_dirs_nginx
call :create_dirs
if not exist "ssl" mkdir ssl
goto :eof

:create_nginx_config
if not exist "nginx.conf" (
    echo [INFO] 创建默认 Nginx 配置文件...
    (
        echo events {
        echo     worker_connections 1024;
        echo }
        echo.
        echo http {
        echo     upstream frontend {
        echo         server frontend:3000;
        echo     }
        echo.
        echo     server {
        echo         listen 80;
        echo         server_name _;
        echo.
        echo         location / {
        echo             proxy_pass http://frontend;
        echo             proxy_set_header Host $host;
        echo             proxy_set_header X-Real-IP $remote_addr;
        echo             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        echo             proxy_set_header X-Forwarded-Proto $scheme;
        echo         }
        echo     }
        echo }
    ) > nginx.conf
    echo [INFO] 已创建默认 Nginx 配置文件
)
goto :eof

:start_services
echo [INFO] 启动服务...
if not exist "docker-compose.prod.yml" (
    echo [ERROR] docker-compose.prod.yml 文件不存在
    pause
    exit /b 1
)
call :create_dirs
docker-compose -f docker-compose.prod.yml up -d
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    pause
    exit /b 1
)
echo [INFO] 服务启动完成
goto :eof

:start_services_nginx
echo [INFO] 启动服务（包含 Nginx）...
if not exist "docker-compose.prod.yml" (
    echo [ERROR] docker-compose.prod.yml 文件不存在
    pause
    exit /b 1
)
call :create_dirs_nginx
call :create_nginx_config
docker-compose -f docker-compose.prod.yml --profile nginx up -d
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    pause
    exit /b 1
)
echo [INFO] 服务启动完成（包含 Nginx）
goto :eof

:show_status
echo [INFO] 服务状态:
docker-compose -f docker-compose.prod.yml ps
echo.
echo [INFO] 服务日志 (最近 20 行):
docker-compose -f docker-compose.prod.yml logs --tail=20
goto :eof

:full_deploy
call :check_docker
call :login_aliyun
call :pull_images
call :stop_containers
call :start_services
call :show_status
echo [INFO] 完整部署完成!
pause
goto menu

:full_deploy_nginx
call :check_docker
call :login_aliyun
call :pull_images
call :stop_containers
call :start_services_nginx
call :show_status
echo [INFO] 完整部署完成（包含 Nginx）!
pause
goto menu

:pull_only
call :check_docker
call :login_aliyun
call :pull_images
echo [INFO] 镜像拉取完成!
pause
goto menu

:start_only
call :check_docker
call :stop_containers
call :start_services
call :show_status
echo [INFO] 服务启动完成!
pause
goto menu

:start_nginx_only
call :check_docker
call :stop_containers
call :start_services_nginx
call :show_status
echo [INFO] 服务启动完成（包含 Nginx）!
pause
goto menu

:stop_only
call :stop_containers
echo [INFO] 服务停止完成!
pause
goto menu

:status_only
call :show_status
pause
goto menu

:end
echo 再见!
pause
