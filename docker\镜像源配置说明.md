# Docker 国内镜像源配置说明

## 重要说明

根据阿里云官方文档，推荐使用**镜像加速器**而不是直接指定镜像仓库地址。镜像加速器会自动加速 Docker Hub 官方镜像的下载，无需修改镜像名称。

## 1. 获取阿里云镜像加速器地址（推荐）

### 步骤 1：获取个人专属加速器地址

1. 登录 [阿里云容器镜像服务控制台](https://cr.console.aliyun.com)
2. 在左侧导航栏选择 **镜像工具** > **镜像加速器**
3. 在 **镜像加速器** 页面获取您的专属 **加速器地址**
4. 地址格式类似：`https://xxx.mirror.aliyuncs.com`

### 步骤 2：配置镜像加速器

#### Windows 系统配置

1. 打开 Docker Desktop
2. 点击右上角的设置图标（齿轮）
3. 选择 "Docker Engine"
4. 将 `docker/daemon.json` 文件的内容复制到配置框中
5. **重要**：将 `https://xxx.mirror.aliyuncs.com` 替换为您在步骤 1 中获取的专属地址
6. 点击 "Apply & Restart" 重启 Docker

### Linux 系统配置

1. 创建或编辑 `/etc/docker/daemon.json` 文件：

```bash
sudo mkdir -p /etc/docker
sudo cp docker/daemon.json /etc/docker/daemon.json
```

2. 重启 Docker 服务：

```bash
sudo systemctl daemon-reload
sudo systemctl restart docker
```

### macOS 系统配置

1. 打开 Docker Desktop
2. 点击菜单栏的 Docker 图标
3. 选择 "Preferences" -> "Docker Engine"
4. 将 `docker/daemon.json` 文件的内容复制到配置框中
5. 点击 "Apply & Restart" 重启 Docker

## 2. 验证镜像源配置

配置完成后，可以通过以下命令验证：

```bash
# 查看 Docker 信息，确认镜像源配置
docker info

# 测试拉取镜像速度
docker pull hello-world
```

## 3. 可用的国内镜像源

### 阿里云镜像源

- 地址：`https://registry.cn-hangzhou.aliyuncs.com`
- 特点：稳定性好，速度快
- 官方文档：https://help.aliyun.com/document_detail/60750.html

### 网易镜像源

- 地址：`https://hub-mirror.c.163.com`
- 特点：免费使用，速度较快

### 腾讯云镜像源

- 地址：`https://ccr.ccs.tencentyun.com`
- 特点：腾讯云用户优先

### DaoCloud 镜像源

- 地址：`https://docker.m.daocloud.io`
- 特点：老牌镜像源，稳定性好

### Azure 中国镜像源

- 地址：`https://dockerhub.azk8s.cn`
- 特点：微软 Azure 提供

### 七牛云镜像源

- 地址：`https://reg-mirror.qiniu.com`
- 特点：七牛云提供，速度不错

## 4. docker-compose.yml 中的镜像配置

项目中的 `docker-compose.yml` 已经配置了国内镜像源：

- **MySQL**: 使用阿里云镜像 `registry.cn-hangzhou.aliyuncs.com/library/mysql:8.0.28`
- **Nginx**: 使用阿里云镜像 `registry.cn-hangzhou.aliyuncs.com/library/nginx:alpine`
- **Node.js**: Dockerfile 中使用 DaoCloud 镜像 `docker.m.daocloud.io/library/node:18-alpine`

## 5. 常见问题

### Q: 镜像拉取仍然很慢怎么办？

A: 可以尝试以下方法：

1. 更换其他镜像源
2. 检查网络连接
3. 使用 VPN 或代理

### Q: 某个镜像源无法访问？

A: 镜像源可能临时维护，可以：

1. 尝试其他镜像源
2. 等待一段时间后重试
3. 查看镜像源官方公告

### Q: 如何临时使用特定镜像源？

A: 可以在拉取镜像时指定完整地址：

```bash
docker pull registry.cn-hangzhou.aliyuncs.com/library/nginx:alpine
```

## 6. 注意事项

1. **配置优先级**: daemon.json 中的配置会被 Docker Desktop 的 GUI 配置覆盖
2. **重启要求**: 修改 daemon.json 后必须重启 Docker 服务
3. **网络环境**: 不同地区的网络环境可能影响镜像源的访问速度
4. **镜像同步**: 国内镜像源可能存在同步延迟，最新镜像可能需要等待

## 7. 推荐配置

根据使用经验，推荐的镜像源优先级：

1. 阿里云（稳定性最好）
2. DaoCloud（老牌可靠）
3. 网易（免费好用）
4. 腾讯云（腾讯云用户优先）
