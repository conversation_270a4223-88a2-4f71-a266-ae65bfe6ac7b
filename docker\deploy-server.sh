#!/bin/bash

# 服务器部署脚本
# 用于在服务器上拉取并启动 TradingAgents 应用

set -e  # 遇到错误时退出

echo "=== TradingAgents 服务器部署脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_info "Docker 环境检查通过"
}

# 登录阿里云容器镜像服务
login_aliyun() {
    print_info "登录阿里云容器镜像服务..."
    
    # 提示用户输入密码
    echo "请输入阿里云容器镜像服务密码:"
    docker login --username=aliyun1315382626 crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
    
    if [ $? -eq 0 ]; then
        print_info "阿里云容器镜像服务登录成功"
    else
        print_error "阿里云容器镜像服务登录失败"
        exit 1
    fi
}

# 拉取最新镜像
pull_images() {
    print_info "拉取最新镜像..."
    
    # 拉取前端镜像
    print_info "拉取前端镜像..."
    docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
    
    # 拉取数据库镜像
    print_info "拉取数据库镜像..."
    docker pull crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28
    
    # 拉取 Nginx 镜像（如果需要）
    print_info "拉取 Nginx 镜像..."
    docker pull nginx:alpine
    
    print_info "所有镜像拉取完成"
}

# 停止现有容器
stop_containers() {
    print_info "停止现有容器..."
    
    if [ -f "docker-compose.prod.yml" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        print_warning "docker-compose.prod.yml 文件不存在，跳过停止容器步骤"
    fi
}

# 启动服务
start_services() {
    print_info "启动服务..."
    
    if [ ! -f "docker-compose.prod.yml" ]; then
        print_error "docker-compose.prod.yml 文件不存在"
        exit 1
    fi
    
    # 创建必要的目录
    mkdir -p mysql/init
    
    # 启动服务（不包含 nginx）
    docker-compose -f docker-compose.prod.yml up -d
    
    print_info "服务启动完成"
}

# 启动服务（包含 nginx）
start_services_with_nginx() {
    print_info "启动服务（包含 Nginx）..."
    
    if [ ! -f "docker-compose.prod.yml" ]; then
        print_error "docker-compose.prod.yml 文件不存在"
        exit 1
    fi
    
    # 创建必要的目录
    mkdir -p mysql/init
    mkdir -p ssl
    
    # 检查 nginx.conf 是否存在
    if [ ! -f "nginx.conf" ]; then
        print_warning "nginx.conf 文件不存在，将创建默认配置"
        create_nginx_config
    fi
    
    # 启动服务（包含 nginx）
    docker-compose -f docker-compose.prod.yml --profile nginx up -d
    
    print_info "服务启动完成（包含 Nginx）"
}

# 创建默认 Nginx 配置
create_nginx_config() {
    cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name _;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF
    print_info "已创建默认 Nginx 配置文件"
}

# 显示服务状态
show_status() {
    print_info "服务状态:"
    docker-compose -f docker-compose.prod.yml ps
    
    print_info "服务日志 (最近 20 行):"
    docker-compose -f docker-compose.prod.yml logs --tail=20
}

# 主函数
main() {
    echo "请选择操作:"
    echo "1. 完整部署 (登录 + 拉取镜像 + 启动服务)"
    echo "2. 完整部署 + Nginx (登录 + 拉取镜像 + 启动服务 + Nginx)"
    echo "3. 仅拉取镜像"
    echo "4. 仅启动服务"
    echo "5. 仅启动服务 + Nginx"
    echo "6. 停止服务"
    echo "7. 查看服务状态"
    
    read -p "请输入选项 (1-7): " choice
    
    case $choice in
        1)
            check_docker
            login_aliyun
            pull_images
            stop_containers
            start_services
            show_status
            ;;
        2)
            check_docker
            login_aliyun
            pull_images
            stop_containers
            start_services_with_nginx
            show_status
            ;;
        3)
            check_docker
            login_aliyun
            pull_images
            ;;
        4)
            check_docker
            stop_containers
            start_services
            show_status
            ;;
        5)
            check_docker
            stop_containers
            start_services_with_nginx
            show_status
            ;;
        6)
            stop_containers
            ;;
        7)
            show_status
            ;;
        *)
            print_error "无效选项"
            exit 1
            ;;
    esac
    
    print_info "操作完成!"
}

# 运行主函数
main
