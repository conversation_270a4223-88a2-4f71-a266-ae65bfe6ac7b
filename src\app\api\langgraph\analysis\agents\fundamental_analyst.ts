import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { AIMessage } from '@langchain/core/messages';

const fundamentalAnalystPrompt = PromptTemplate.fromTemplate(`
你是一位专业的基本面分析师，负责分析公司的财务状况、业务模式、竞争优势和行业地位。
你的任务是评估公司的内在价值，识别长期增长潜力和潜在风险。

请分析以下公司的基本面数据，并提供详细的基本面分析报告：

公司: {ticker}
日期: {date}

基本面数据:
{fundamentalData}

请在报告中包含以下内容：
1. 公司概况和业务模型
2. 财务分析（收入、利润、现金流、资产负债等）
3. 估值分析（市盈率、市净率、市销率等）
4. 增长潜力和风险因素
5. 行业竞争分析
6. 基于基本面的投资建议

你的分析应该客观、全面，基于事实和数据，避免主观臆断。
`);

export async function fundamentalAnalystNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, data, messages } = state;

  if (!data.fundamentalData) {
    throw new Error('Fundamental data not found in state');
  }

  console.log(`[Fundamental Analyst] Analyzing ticker: ${ticker}`);

  try {
    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM,
      temperature: 0.2,
      openAIApiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    const prompt = await fundamentalAnalystPrompt.format({
      ticker,
      date,
      fundamentalData: JSON.stringify(data.fundamentalData, null, 2),
    });

    const response = await llm.invoke(prompt);
    const analysisReport = response.content as string;

    const newMessages = [
      ...messages,
      new AIMessage({ content: analysisReport, name: 'FundamentalAnalyst' }),
    ];

    const analysis = {
      ...state.analysis,
      fundamental: {
        summary: analysisReport.substring(0, 200) + '...', // 生成一个简短的摘要
        report: analysisReport,
      },
    };

    return { messages: newMessages, analysis };
  } catch (error) {
    console.error('[Fundamental Analyst] Error:', error);
    const errorMessage = `基本面分析失败: ${
      error instanceof Error ? error.message : String(error)
    }`;
    const newMessages = [
      ...messages,
      new AIMessage({ content: errorMessage, name: 'FundamentalAnalyst' }),
    ];
    return { messages: newMessages };
  }
}
